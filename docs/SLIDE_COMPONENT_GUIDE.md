# スライドコンポーネント作成ガイド

このガイドでは、新しいスライドコンポーネントを作成する際のルールとベストプラクティスについて説明します。

## 📁 新しいフォルダー構成（v2.1）

プロジェクトのフォルダー構成が整理され、コードの可読性が向上しました：

```
components/                    # スライドコンポーネント専用
├── slides/                   # 個別スライドコンポーネント
│   ├── CoverSlide.tsx
│   ├── ExecutiveSummarySlide.tsx
│   └── [その他のスライド]
└── slide-parts/              # スライド共通パーツ
    ├── SlideBody.tsx
    ├── SlideKeyMessage.tsx
    ├── SlideLabel.tsx
    └── index.ts              # 統一エクスポート

features/                     # 機能別コンポーネント
├── slide-generator/          # スライド生成機能
├── pdf-export/               # PDF出力機能
├── presentation/             # プレゼンテーション機能
└── template-viewer/          # テンプレート表示機能

ui/                          # 汎用UIコンポーネント
layout/                      # レイアウトコンポーネント
```

### 利点
- **明確な責任分離**: スライドコンポーネントと他の機能が分離
- **機能別組織化**: 関連する機能がまとまっている
- **スケーラビリティ**: 新しい機能を追加しやすい
- **保守性**: コードの場所が予測しやすい

## 📋 目次

1. [基本的なルール](#基本的なルール)
2. [コンポーネントの構造](#コンポーネントの構造)
3. [メタデータの定義](#メタデータの定義)
4. [AIエディターでの使用方法](#aiエディターでの使用方法)
5. [実装例](#実装例)
6. [テスト方法](#テスト方法)

## 🎯 基本的なルール

### 1. ファイル命名規則
- ファイル名は `PascalCase` で記述する
- ファイル拡張子は `.tsx` を使用する
- ファイルは `components/slides/` ディレクトリに配置する

**例**: `MyCustomSlide.tsx`

### 2. コンポーネント命名規則
- コンポーネント名はファイル名と同じにする
- 必ず `Slide` で終わるか、図表系の場合は `Diagram` で終わる

**例**: `MyCustomSlide`, `ProcessDiagram`

### 3. Client Componentの指定
- 全てのスライドコンポーネントは`'use client'`ディレクティブを追加する
- Next.js App Routerでクライアントサイド機能を使用するため

```typescript
// ✅ 正しい
'use client';

import React from 'react';
// ...
```

### 4. エクスポート方式
- デフォルトエクスポートを使用する
- 名前付きエクスポートは使用しない

```typescript
// ✅ 正しい
export default function MyCustomSlide(props: MyCustomSlideProps) {
  // ...
}

// ❌ 間違い
export function MyCustomSlide(props: MyCustomSlideProps) {
  // ...
}
```

## 🏗️ コンポーネントの構造

### 基本テンプレート

```typescript
'use client';

import React from 'react';
import { SlideBody, SlideKeyMessage, SlideLabel } from '@/components/slide-parts';

interface MyCustomSlideProps {
  title: string;
  content: string;
  // その他必要なプロパティ
}

export default function MyCustomSlide({
  title,
  content,
  ...otherProps
}: MyCustomSlideProps) {
  return (
    <div className="w-full h-full flex flex-col bg-white">
      {/* スライドラベル（オプション） */}
      <SlideLabel text="カスタムスライド" />
      
      {/* キーメッセージ */}
      <SlideKeyMessage message={title} />
      
      {/* メインコンテンツ */}
      <SlideBody>
        <div className="space-y-4">
          {/* ここにスライドの内容を実装 */}
          <p>{content}</p>
        </div>
      </SlideBody>
    </div>
  );
}
```

### 必須要素

1. **TypeScript型定義**: プロパティの型を明確に定義する
2. **レスポンシブデザイン**: 様々な画面サイズに対応する
3. **アクセシビリティ**: 適切なARIA属性を設定する
4. **共通コンポーネントの活用**: `SlideParts` の使用を推奨

## 📝 メタデータの定義

新しいコンポーネントを作成したら、`utils/componentDiscovery.ts` にメタデータを追加する必要があります。

### メタデータの構造

```typescript
{
  component: 'MyCustomSlide',           // コンポーネント名
  slideType: 'custom',                 // スライドタイプ（一意）
  description: 'カスタムスライドの説明',  // 日本語での説明
  conditions: [                        // 使用条件（オプション）
    '特定の条件1',
    '特定の条件2'
  ],
  required: false,                     // 必須かどうか
  order: 60,                          // 表示順序
  category: 'custom'                   // カテゴリ
}
```

### カテゴリ一覧

- `core`: 必須コンポーネント
- `analysis`: 分析系
- `solution`: ソリューション系
- `financial`: 財務・ROI系
- `planning`: 計画・スケジュール系
- `measurement`: 測定・指標系
- `evidence`: 事例・証拠系
- `visualization`: 図表・可視化系
- `custom`: カスタム

### メタデータの追加方法

`utils/componentDiscovery.ts` の `AVAILABLE_SLIDE_COMPONENTS` 配列に追加：

```typescript
{
  component: 'MyCustomSlide',
  slideType: 'custom',
  description: 'カスタムスライドの説明',
  conditions: ['特定の条件が満たされている'],
  order: 60,
  category: 'custom'
}
```

## 🤖 AIエディターでの使用方法

### メンション機能

AIエディターで新しいスライドコンポーネントを作成する際は、以下のようにメンションしてください：

```
@スライドコンポーネント作成

新しいスライドコンポーネントを作成してください。

要件:
- コンポーネント名: ProcessFlowSlide
- 用途: プロセスフローの可視化
- 必要なプロパティ: 
  - title: string
  - steps: Array<{id: string, name: string, description: string}>
  - connections: Array<{from: string, to: string}>
- スタイル: モダンでクリーンなデザイン
- カテゴリ: visualization
```

### AIが自動生成する内容

1. **コンポーネントファイル**: `components/slides/ProcessFlowSlide.tsx`
2. **型定義**: TypeScriptインターフェース
3. **メタデータ更新**: `componentDiscovery.ts` への追加
4. **基本スタイリング**: Tailwind CSSを使用

## 💡 実装例

### シンプルなテキストスライド

```typescript
'use client';

import React from 'react';
import { SlideBody, SlideKeyMessage } from '@/components/slide-parts';

interface SimpleTextSlideProps {
  title: string;
  bullets: string[];
}

export default function SimpleTextSlide({ title, bullets }: SimpleTextSlideProps) {
  return (
    <div className="w-full h-full flex flex-col bg-white">
      <SlideKeyMessage message={title} />
      <SlideBody>
        <ul className="space-y-3 text-lg">
          {bullets.map((bullet, index) => (
            <li key={index} className="flex items-start">
              <span className="text-blue-600 mr-3">•</span>
              <span>{bullet}</span>
            </li>
          ))}
        </ul>
      </SlideBody>
    </div>
  );
}
```

### 図表を含むスライド

```typescript
'use client';

import React from 'react';
import { SlideBody, SlideKeyMessage } from '@/components/slide-parts';

interface ChartSlideProps {
  title: string;
  data: Array<{ label: string; value: number }>;
}

export default function ChartSlide({ title, data }: ChartSlideProps) {
  const maxValue = Math.max(...data.map(d => d.value));

  return (
    <div className="w-full h-full flex flex-col bg-white">
      <SlideKeyMessage message={title} />
      <SlideBody>
        <div className="space-y-4">
          {data.map((item, index) => (
            <div key={index} className="flex items-center space-x-4">
              <div className="w-24 text-sm font-medium">{item.label}</div>
              <div className="flex-1 bg-gray-200 rounded-full h-6">
                <div
                  className="bg-blue-600 h-6 rounded-full flex items-center justify-end pr-2"
                  style={{ width: `${(item.value / maxValue) * 100}%` }}
                >
                  <span className="text-white text-xs font-bold">{item.value}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </SlideBody>
    </div>
  );
}
```

## 🧪 テスト方法

### 1. 開発環境での確認

```bash
npm run dev
```

ブラウザで `http://localhost:3000/generator` にアクセスし、新しいコンポーネントが正しく動作することを確認。

### 2. コンポーネントの動的読み込み確認

ブラウザの開発者ツールのコンソールで以下を確認：

```
動的スライドコンポーネントを初期化中...
利用可能なコンポーネント: [コンポーネント名のリスト]
```

### 3. AIエージェントでのテスト

議事録を入力してスライド生成を実行し、新しいコンポーネントが適切に選択・使用されることを確認。

## 🚨 注意事項

1. **Client Componentの必須指定**: 全てのスライドコンポーネントに`'use client'`を追加する
2. **パフォーマンス**: 重い処理は避け、必要に応じて `useMemo` や `useCallback` を使用
3. **エラーハンドリング**: プロパティが不正な場合の適切な処理を実装
4. **スタイル統一**: 既存のコンポーネントとデザインの一貫性を保つ
5. **メタデータ更新**: コンポーネント作成後は必ずメタデータを更新する

### Next.js App Routerでの注意点

- **Server vs Client Components**: スライドコンポーネントはクライアントサイドで動作するため、必ず`'use client'`を追加
- **動的インポート**: サーバーサイドでの動的インポートを避けるため、AIエージェントでは静的定義を使用
- **React Hooks**: `useEffect`、`useState`などのHooksを使用する場合は`'use client'`が必須

## 📞 サポート

問題が発生した場合は、以下を確認してください：

1. コンソールエラーの有無
2. メタデータの正確性
3. ファイル名とコンポーネント名の一致
4. 型定義の正確性

このガイドに従って作成されたコンポーネントは、AIエージェントによって自動的に発見・選択され、適切なスライドとして使用されます。
