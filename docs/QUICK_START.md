# 🚀 Spectacle Claude クイックスタート

## 概要

Spectacle Claudeは、議事録から自動的にプレゼンテーションスライドを生成するAI駆動のツールです。開発者が簡単にセットアップして、すぐに使い始めることができます。

## ⚡ 1分でセットアップ

### 前提条件
- Node.js 18.0以上
- npm または yarn
- OpenAI API キー

### インストール

```bash
# リポジトリをクローン
git clone https://github.com/your-org/spectacle-claude
cd spectacle-claude

# 依存関係をインストール
npm install

# 環境変数を設定
cp .env.example .env.local
# .env.localファイルを編集してOpenAI API キーを設定

# 開発サーバーを起動
npm run dev
```

### 環境変数の設定

`.env.local`ファイルに以下を設定：

```env
OPENAI_API_KEY=your_openai_api_key_here
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 🎯 基本的な使い方

### 1. ブラウザでアクセス
```
http://localhost:3000
```

### 2. サンプル議事録を試す
プロジェクトには3つのサンプル議事録が含まれています：

- `examples/sales-meeting.md` - 営業・提案会議
- `examples/project-review.md` - プロジェクトレビュー
- `examples/strategic-planning.md` - 戦略企画会議

### 3. スライド生成の流れ

1. **議事録入力**: テキストエリアに議事録を貼り付け
2. **AI生成**: 「スライドを生成」ボタンをクリック
3. **編集**: 生成されたスライドを編集・並び替え
4. **出力**: PDFとして出力またはプレゼンテーション表示

## 📊 生成されるスライド構成

### 必須スライド（7枚）
1. **表紙** - プレゼンテーションのタイトル
2. **アジェンダ** - 会議の進行予定
3. **本日の目的** - 会議の目標
4. **現状・課題** - 問題の分析
5. **目指すゴール** - 理想的な状態
6. **開発の流れ** - プロジェクト計画
7. **次回に向けて** - アクションプラン

### オプションスライド
内容に応じて自動選択：
- エグゼクティブサマリー
- ROI計算
- タイムライン
- 比較表
- メトリクス
- 事例紹介
- 図表（ベン図、サイクル図など）

## 🛠️ カスタマイズ

### 新しいスライドコンポーネントの追加

1. `components/slides/`に新しいコンポーネントを作成
2. `agents/templates/slideTemplate.ts`にテンプレート定義を追加
3. `agents/nodes/generateSlides.ts`に生成指示を追加

詳細は[スライドコンポーネントガイド](./SLIDE_COMPONENT_GUIDE.md)を参照。

### デザインのカスタマイズ

統一されたカラーパレット：
- **プライマリーブルー**: `#143d8f`
- **ホワイト**: `#ffffff`
- **ブラック**: `#000000`

## 🔧 開発者向け機能

### デバッグモード
```bash
# デバッグ情報を表示
DEBUG=true npm run dev
```

### モックモード
```bash
# AIを使わずにモックデータでテスト
MOCK_MODE=true npm run dev
```

### ビルドとデプロイ
```bash
# 本番ビルド
npm run build

# 本番サーバー起動
npm start
```

## 📝 サンプル議事録の使い方

### 営業会議サンプル
```bash
cat examples/sales-meeting.md
```
このサンプルをコピーして「AI生成」タブで試してください。

### プロジェクトレビューサンプル
```bash
cat examples/project-review.md
```
進捗管理や課題分析のスライドが生成されます。

### 戦略企画サンプル
```bash
cat examples/strategic-planning.md
```
経営層向けの戦略的なプレゼンテーションが生成されます。

## 🎨 UI機能

### スライド編集
- **ドラッグ&ドロップ**: スライドの順序変更
- **選択削除**: 不要なスライドの削除
- **再生成**: 選択したスライドの再生成
- **プレビュー**: フルスクリーンプレビュー

### PDF出力
- 高品質なPDF生成
- プレゼンテーション形式
- カスタムファイル名

## 🚨 トラブルシューティング

### よくある問題

#### 1. OpenAI API エラー
```
Error: OpenAI API key not found
```
**解決策**: `.env.local`ファイルでAPI キーが正しく設定されているか確認

#### 2. スライドが生成されない
```
Error: Failed to generate slides
```
**解決策**: 
- 議事録の内容が十分な情報を含んでいるか確認
- API キーの使用制限を確認

#### 3. PDF出力エラー
```
Error: PDF export failed
```
**解決策**: 
- ブラウザのポップアップブロックを無効化
- 十分なメモリがあることを確認

### ログの確認
```bash
# 開発サーバーのログを確認
npm run dev

# ブラウザの開発者ツールでコンソールを確認
F12 → Console
```

## 📚 さらに詳しく

- [スライドコンポーネントガイド](./SLIDE_COMPONENT_GUIDE.md)
- [API リファレンス](./API_REFERENCE.md)
- [アーキテクチャ概要](./ARCHITECTURE.md)

## 🤝 コントリビューション

1. フォークしてブランチを作成
2. 変更を実装
3. テストを実行
4. プルリクエストを作成

## 📄 ライセンス

MIT License - 詳細は[LICENSE](../LICENSE)ファイルを参照

---

**🎉 これで準備完了！** 

`npm run dev`を実行して、`http://localhost:3000`にアクセスしてSpectacle Claudeを体験してください。
