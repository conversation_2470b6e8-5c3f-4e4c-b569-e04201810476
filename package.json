{"name": "spectaclev00", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hello-pangea/dnd": "^18.0.1", "@langchain/community": "^0.3.44", "@langchain/core": "^0.3.57", "@langchain/langgraph": "^0.2.73", "@langchain/openai": "^0.5.11", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@stagewise/toolbar-next": "^0.1.2", "@tailwindcss/line-clamp": "^0.4.4", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/ws": "^8.18.1", "autoprefixer": "^10.4.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "langchain": "^0.3.27", "lucide-react": "^0.511.0", "next": "^14.0.4", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "spectacle": "^10.1.6", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "twin.macro": "^3.4.1", "typescript": "^5.3.3", "ws": "^8.18.2", "zod": "^3.25.28"}}