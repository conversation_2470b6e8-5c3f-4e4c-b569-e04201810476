import { z } from 'zod';
import { MeetingClassification } from './classifyMeetingNotes';
import { getAllSlideTemplates } from '../templates/slideTemplate';

// スライド構成レビューの結果スキーマ
export const slideStructureReviewSchema = z.object({
  approvedStructure: z.array(z.object({
    slideType: z.string(),
    component: z.string(),
    props: z.any(),
    order: z.number(),
    approved: z.boolean().default(true)
  })),
  userModifications: z.object({
    addedSlides: z.array(z.string()).default([]),
    removedSlides: z.array(z.string()).default([]),
    reorderedSlides: z.array(z.object({
      component: z.string(),
      newOrder: z.number()
    })).default([])
  }).default({}),
  requiresRegeneration: z.boolean().default(false),
  feedback: z.string().optional()
});

export type SlideStructureReview = z.infer<typeof slideStructureReviewSchema>;

export interface ReviewSlideStructureInput {
  slideStructure: any[];
  extractedInfo: any;
  classification?: MeetingClassification;
  userPreferences?: {
    maxSlides?: number;
    preferredSlideTypes?: string[];
    excludedSlideTypes?: string[];
  };
}

export interface ReviewSlideStructureOutput {
  review: SlideStructureReview;
  finalStructure: any[];
}

export class ReviewSlideStructureAgent {
  /**
   * スライド構成をレビューし、ユーザーの調整を適用する
   */
  async execute(input: ReviewSlideStructureInput): Promise<ReviewSlideStructureOutput> {
    try {
      console.log('ReviewSlideStructureAgent: スライド構成のレビューを開始します');
      
      // 初期構成の分析
      const initialAnalysis = this.analyzeInitialStructure(input.slideStructure, input.classification);
      
      // ユーザー設定に基づく調整
      const adjustedStructure = this.applyUserPreferences(input.slideStructure, input.userPreferences);
      
      // 構成の最適化
      const optimizedStructure = this.optimizeStructure(adjustedStructure, input.extractedInfo, input.classification);
      
      // レビュー結果の生成
      const review: SlideStructureReview = {
        approvedStructure: optimizedStructure.map(slide => ({
          ...slide,
          approved: true
        })),
        userModifications: {
          addedSlides: [],
          removedSlides: [],
          reorderedSlides: []
        },
        requiresRegeneration: this.checkRegenerationNeeded(input.slideStructure, optimizedStructure),
        feedback: initialAnalysis.feedback
      };
      
      console.log('ReviewSlideStructureAgent: レビュー完了');
      console.log('ReviewSlideStructureAgent: 最終スライド数:', optimizedStructure.length);
      console.log('ReviewSlideStructureAgent: 再生成が必要:', review.requiresRegeneration);
      
      return {
        review,
        finalStructure: optimizedStructure
      };
    } catch (error) {
      console.error('ReviewSlideStructureAgent: エラーが発生しました:', error);
      throw error;
    }
  }

  /**
   * 初期構成を分析してフィードバックを生成
   */
  private analyzeInitialStructure(structure: any[], classification?: MeetingClassification): { feedback: string } {
    const slideCount = structure.length;
    const slideTypes = structure.map(s => s.slideType);
    const components = structure.map(s => s.component);
    
    let feedback = `提案されたスライド構成（${slideCount}枚）:\n`;
    feedback += components.map((comp, index) => `${index + 1}. ${comp}`).join('\n');
    
    if (classification) {
      feedback += `\n\n会議タイプ（${classification.meetingType}）に基づく分析:\n`;
      
      // 会議タイプに適した構成かチェック
      const typeSpecificRecommendations = this.getTypeSpecificRecommendations(classification.meetingType);
      const missingRecommended = typeSpecificRecommendations.filter(rec => 
        !components.includes(rec)
      );
      
      if (missingRecommended.length > 0) {
        feedback += `- 推奨されているが含まれていないスライド: ${missingRecommended.join(', ')}\n`;
      }
      
      // スライド数の適切性
      if (slideCount < 5) {
        feedback += `- スライド数が少なめです。追加のコンテンツを検討してください。\n`;
      } else if (slideCount > 10) {
        feedback += `- スライド数が多めです。重要なポイントに絞ることを検討してください。\n`;
      } else {
        feedback += `- スライド数は適切です。\n`;
      }
    }
    
    return { feedback };
  }

  /**
   * ユーザー設定に基づいて構成を調整
   */
  private applyUserPreferences(structure: any[], preferences?: ReviewSlideStructureInput['userPreferences']): any[] {
    if (!preferences) return structure;
    
    let adjustedStructure = [...structure];
    
    // 除外するスライドタイプを削除
    if (preferences.excludedSlideTypes?.length) {
      adjustedStructure = adjustedStructure.filter(slide => 
        !preferences.excludedSlideTypes!.includes(slide.slideType)
      );
    }
    
    // 最大スライド数の制限
    if (preferences.maxSlides && adjustedStructure.length > preferences.maxSlides) {
      // 優先度の低いスライドから削除（必須スライドは保持）
      const coreSlides = adjustedStructure.filter(slide => 
        ['cover', 'summary', 'next_steps'].includes(slide.slideType)
      );
      const optionalSlides = adjustedStructure.filter(slide => 
        !['cover', 'summary', 'next_steps'].includes(slide.slideType)
      );
      
      const maxOptional = preferences.maxSlides - coreSlides.length;
      adjustedStructure = [
        ...coreSlides,
        ...optionalSlides.slice(0, Math.max(0, maxOptional))
      ];
    }
    
    // orderでソート
    adjustedStructure.sort((a, b) => a.order - b.order);
    
    return adjustedStructure;
  }

  /**
   * 構成を最適化
   */
  private optimizeStructure(structure: any[], extractedInfo: any, classification?: MeetingClassification): any[] {
    // 情報の充実度に基づいてスライドの必要性を判定
    const optimizedStructure = structure.filter(slide => {
      return this.isSlideNecessary(slide, extractedInfo, classification);
    });
    
    // 順序の最適化
    return this.optimizeSlideOrder(optimizedStructure, classification);
  }

  /**
   * スライドが必要かどうかを判定
   */
  private isSlideNecessary(slide: any, extractedInfo: any, classification?: MeetingClassification): boolean {
    // 必須スライドは常に含める
    if (['cover', 'summary', 'next_steps'].includes(slide.slideType)) {
      return true;
    }
    
    // 情報の有無に基づく判定
    switch (slide.component) {
      case 'ProblemAnalysisSlide':
        return extractedInfo.currentChallenges?.length > 0;
      case 'ROICalculationSlide':
        return extractedInfo.roi && (
          extractedInfo.roi.investment?.length > 0 || 
          extractedInfo.roi.returns?.length > 0 ||
          extractedInfo.roi.percentage > 0
        );
      case 'TimelineSlide':
        return extractedInfo.timeline?.length > 0;
      case 'SolutionOverviewSlide':
        return extractedInfo.proposedSolutions?.length > 0;
      case 'AsIsSlide':
        return extractedInfo.currentChallenges?.length > 0 && extractedInfo.idealState?.length > 0;
      default:
        return true; // その他のスライドは含める
    }
  }

  /**
   * スライドの順序を最適化
   */
  private optimizeSlideOrder(structure: any[], classification?: MeetingClassification): any[] {
    // 会議タイプに基づく最適な順序を定義
    const orderPreferences: Record<string, string[]> = {
      'sales_presentation': ['cover', 'summary', 'problem', 'solution', 'roi', 'timeline', 'next_steps'],
      'project_review': ['cover', 'summary', 'timeline', 'metrics', 'comparison', 'next_steps'],
      'problem_solving': ['cover', 'summary', 'problem', 'as_is', 'solution', 'next_steps'],
      'strategic_planning': ['cover', 'summary', 'as_is', 'timeline', 'roi', 'metrics', 'next_steps']
    };
    
    const preferredOrder = classification ? 
      orderPreferences[classification.meetingType] || [] : [];
    
    if (preferredOrder.length === 0) {
      return structure.sort((a, b) => a.order - b.order);
    }
    
    // 優先順序に基づいてソート
    return structure.sort((a, b) => {
      const aIndex = preferredOrder.indexOf(a.slideType);
      const bIndex = preferredOrder.indexOf(b.slideType);
      
      if (aIndex === -1 && bIndex === -1) return a.order - b.order;
      if (aIndex === -1) return 1;
      if (bIndex === -1) return -1;
      
      return aIndex - bIndex;
    }).map((slide, index) => ({
      ...slide,
      order: index + 1
    }));
  }

  /**
   * 再生成が必要かどうかをチェック
   */
  private checkRegenerationNeeded(original: any[], optimized: any[]): boolean {
    if (original.length !== optimized.length) return true;
    
    for (let i = 0; i < original.length; i++) {
      if (original[i].component !== optimized[i].component) return true;
      if (original[i].order !== optimized[i].order) return true;
    }
    
    return false;
  }

  /**
   * 会議タイプ別の推奨スライドを取得
   */
  private getTypeSpecificRecommendations(meetingType: string): string[] {
    const recommendations: Record<string, string[]> = {
      'sales_presentation': ['ProblemAnalysisSlide', 'SolutionOverviewSlide', 'ROICalculationSlide'],
      'project_review': ['TimelineSlide', 'MetricsSlide', 'ComparisonTableSlide'],
      'problem_solving': ['ProblemAnalysisSlide', 'AsIsSlide', 'SolutionOverviewSlide'],
      'strategic_planning': ['AsIsSlide', 'TimelineSlide', 'ROICalculationSlide']
    };
    
    return recommendations[meetingType] || [];
  }
}
