import { ChatOpenAI } from '@langchain/openai';
import { z } from 'zod';

// 議事録分類の結果スキーマ
export const meetingClassificationSchema = z.object({
  meetingType: z.enum([
    'sales_presentation',      // 営業・提案
    'project_review',         // プロジェクトレビュー
    'problem_solving',        // 問題解決
    'strategic_planning',     // 戦略企画
    'status_update',          // 進捗報告
    'training_session',       // 研修・トレーニング
    'client_consultation',    // クライアント相談
    'product_demo',          // 製品デモ
    'budget_planning',       // 予算計画
    'general_meeting'        // 一般的な会議
  ]),
  primaryThemes: z.array(z.string()).min(1).max(5),
  contentAreas: z.object({
    hasCompanyInfo: z.boolean(),
    hasProblemAnalysis: z.boolean(),
    hasSolutionProposal: z.boolean(),
    hasROIData: z.boolean(),
    hasTimeline: z.boolean(),
    hasNextSteps: z.boolean(),
    hasComparison: z.boolean(),
    hasProcessFlow: z.boolean(),
    hasMetrics: z.boolean(),
    hasRiskAnalysis: z.boolean(),
    hasStakeholderInfo: z.boolean(),
    hasTechnicalDetails: z.boolean()
  }),
  extractionPriority: z.array(z.string()),
  recommendedSlideTypes: z.array(z.string()),
  confidence: z.number().min(0).max(1)
});

export type MeetingClassification = z.infer<typeof meetingClassificationSchema>;

export interface ClassifyMeetingNotesInput {
  meetingNotes: string;
}

export interface ClassifyMeetingNotesOutput {
  classification: MeetingClassification;
}

const CLASSIFY_PROMPT = `
あなたは議事録の内容を分析して、最適なプレゼンテーション戦略を決定する専門家です。
議事録の内容を詳細に分析し、以下の観点で分類してください。

議事録:
{meetingNotes}

【分析観点】
1. 会議の種類・目的の特定
2. 主要なテーマの抽出（最大5つ）
3. 含まれている情報領域の特定
4. 情報抽出の優先順位
5. 推奨されるスライドタイプ
6. 分析の信頼度

【重要】以下のJSON形式でのみ回答してください：

{
  "meetingType": "会議タイプ（以下から選択）",
  "primaryThemes": ["テーマ1", "テーマ2", "テーマ3"],
  "contentAreas": {
    "hasCompanyInfo": true/false,
    "hasProblemAnalysis": true/false,
    "hasSolutionProposal": true/false,
    "hasROIData": true/false,
    "hasTimeline": true/false,
    "hasNextSteps": true/false,
    "hasComparison": true/false,
    "hasProcessFlow": true/false,
    "hasMetrics": true/false,
    "hasRiskAnalysis": true/false,
    "hasStakeholderInfo": true/false,
    "hasTechnicalDetails": true/false
  },
  "extractionPriority": ["優先度順の抽出項目"],
  "recommendedSlideTypes": ["推奨スライドタイプ"],
  "confidence": 0.0-1.0の信頼度
}

【会議タイプ】
- sales_presentation: 営業・提案
- project_review: プロジェクトレビュー
- problem_solving: 問題解決
- strategic_planning: 戦略企画
- status_update: 進捗報告
- training_session: 研修・トレーニング
- client_consultation: クライアント相談
- product_demo: 製品デモ
- budget_planning: 予算計画
- general_meeting: 一般的な会議

【推奨スライドタイプ例】
- cover: 表紙
- summary: サマリー
- problem: 課題分析
- solution: ソリューション
- roi: ROI計算
- timeline: タイムライン
- comparison: 比較表
- process: プロセス図
- metrics: メトリクス
- next_steps: 次のステップ

注意事項：
- 議事録の内容を詳細に分析し、実際に含まれている情報のみをtrueにしてください
- extractionPriorityは重要度順に並べてください
- recommendedSlideTypesは内容に最も適したものを選択してください
- confidenceは分析の確実性を表してください
`;

export class ClassifyMeetingNotesAgent {
  private model: ChatOpenAI;

  constructor() {
    this.model = new ChatOpenAI({
      modelName: 'gpt-4.1-mini',
      temperature: 0.1,
      timeout: 60000
    });
  }

  async execute(input: ClassifyMeetingNotesInput): Promise<ClassifyMeetingNotesOutput> {
    try {
      console.log('ClassifyMeetingNotesAgent: 議事録の分類を開始します');
      
      const prompt = CLASSIFY_PROMPT.replace('{meetingNotes}', input.meetingNotes);
      
      const response = await this.model.invoke([
        {
          role: 'system',
          content: prompt
        },
        {
          role: 'user',
          content: '議事録を分析し、最適なプレゼンテーション戦略を決定してください。'
        }
      ]);

      console.log('ClassifyMeetingNotesAgent: AIからの応答を受信しました');
      
      // レスポンスからJSONを抽出
      const content = response.content as string;
      let classification;
      
      try {
        // 複数パターンでJSON抽出を試みる
        let jsonStr = '';
        
        // パターン1: ```json ... ``` 
        const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonStr = jsonMatch[1];
        } 
        // パターン2: 全体がJSONの場合（直接パース）
        else if (content.trim().startsWith('{')) {
          jsonStr = content.trim();
        }
        // パターン3: { ... } の最初のJSONオブジェクト
        else {
          const startIndex = content.indexOf('{');
          if (startIndex !== -1) {
            let braceCount = 0;
            let endIndex = -1;
            
            for (let i = startIndex; i < content.length; i++) {
              if (content[i] === '{') {
                braceCount++;
              } else if (content[i] === '}') {
                braceCount--;
                if (braceCount === 0) {
                  endIndex = i;
                  break;
                }
              }
            }
            
            if (endIndex !== -1) {
              jsonStr = content.substring(startIndex, endIndex + 1);
            }
          }
        }
        
        if (jsonStr) {
          console.log('ClassifyMeetingNotesAgent: 抽出されたJSON文字列の先頭部分:', jsonStr.substring(0, 200) + '...');
          classification = JSON.parse(jsonStr.trim());
          console.log('ClassifyMeetingNotesAgent: JSONの解析に成功しました');
        } else {
          throw new Error('JSON not found in response');
        }
      } catch (parseError) {
        console.error('ClassifyMeetingNotesAgent: JSONの解析に失敗しました:', parseError);
        console.error('ClassifyMeetingNotesAgent: 元の応答:', content);
        
        // フォールバック：デフォルト分類
        classification = {
          meetingType: 'general_meeting',
          primaryThemes: ['一般的な議論'],
          contentAreas: {
            hasCompanyInfo: true,
            hasProblemAnalysis: false,
            hasSolutionProposal: false,
            hasROIData: false,
            hasTimeline: false,
            hasNextSteps: true,
            hasComparison: false,
            hasProcessFlow: false,
            hasMetrics: false,
            hasRiskAnalysis: false,
            hasStakeholderInfo: false,
            hasTechnicalDetails: false
          },
          extractionPriority: ['companyName', 'mainTopic', 'nextSteps'],
          recommendedSlideTypes: ['cover', 'summary', 'next_steps'],
          confidence: 0.3
        };
        console.log('ClassifyMeetingNotesAgent: デフォルト分類を使用します');
      }
      
      // Zodでバリデーション
      console.log('ClassifyMeetingNotesAgent: データのバリデーションを開始します');
      
      let validatedClassification;
      try {
        validatedClassification = meetingClassificationSchema.parse(classification);
        console.log('ClassifyMeetingNotesAgent: バリデーションに成功しました');
      } catch (validationError) {
        console.error('ClassifyMeetingNotesAgent: バリデーションエラー:', validationError);
        
        // バリデーションエラーの場合、安全なデフォルト値を使用
        validatedClassification = {
          meetingType: 'general_meeting' as const,
          primaryThemes: ['一般的な議論'],
          contentAreas: {
            hasCompanyInfo: true,
            hasProblemAnalysis: false,
            hasSolutionProposal: false,
            hasROIData: false,
            hasTimeline: false,
            hasNextSteps: true,
            hasComparison: false,
            hasProcessFlow: false,
            hasMetrics: false,
            hasRiskAnalysis: false,
            hasStakeholderInfo: false,
            hasTechnicalDetails: false
          },
          extractionPriority: ['companyName', 'mainTopic', 'nextSteps'],
          recommendedSlideTypes: ['cover', 'summary', 'next_steps'],
          confidence: 0.3
        };
        console.log('ClassifyMeetingNotesAgent: デフォルト値を適用しました');
      }
      
      console.log('ClassifyMeetingNotesAgent: 分類結果:', {
        meetingType: validatedClassification.meetingType,
        themes: validatedClassification.primaryThemes,
        confidence: validatedClassification.confidence
      });
      
      return {
        classification: validatedClassification
      };
    } catch (error) {
      console.error('ClassifyMeetingNotesAgent: エラーが発生しました:', error);
      throw error;
    }
  }
}
