import { ChatOpenAI } from '@langchain/openai';
import { z } from 'zod';
import { extractedInfoSchema } from '../schemas';
import { AnalyzeNotesInput, AnalyzeNotesOutput } from '../types';
import { MeetingClassification } from './classifyMeetingNotes';

// 動的プロンプト生成のためのテンプレート
const DYNAMIC_ANALYZE_PROMPT_TEMPLATE = `
あなたは議事録を分析して、プレゼンテーション資料作成に必要な情報を抽出する専門家です。
以下の議事録から、指定された優先順位に基づいて重要な情報を構造化して抽出してください。

【会議分類情報】
会議タイプ: {meetingType}
主要テーマ: {primaryThemes}
信頼度: {confidence}

【抽出対象領域】
{contentAreas}

議事録:
{meetingNotes}

【抽出優先順位】
{extractionPriority}

【重要】以下のJSON形式でのみ回答してください。説明文やMarkdownは含めず、純粋なJSONオブジェクトのみを返してください：

{dynamicSchema}

注意事項：
- 優先順位の高い情報から重点的に抽出してください
- 議事録に含まれていない情報は無理に作成せず、適切なデフォルト値を使用してください
- 影響度と優先度は必ず'high'/'medium'/'low'のいずれかを使用してください
- 数値フィールドには必ず数値を設定してください
- 抽出対象領域に含まれていない情報は簡略化または省略してください
`;

export class AnalyzeNotesAgent {
  private model: ChatOpenAI;

  constructor() {
    this.model = new ChatOpenAI({
      modelName: 'gpt-4.1-mini',  // より高性能なモデルに変更
      temperature: 0.1,     // 出力の安定性を向上
      timeout: 60000        // タイムアウトを設定
    });
  }

  /**
   * 分類結果に基づいて動的なJSONスキーマを生成
   */
  private generateDynamicSchema(classification: MeetingClassification): string {
    const schema: any = {
      companyName: "会社名",
      mainTopic: "メイントピック"
    };

    // 分類結果に基づいて必要なフィールドを追加
    if (classification.contentAreas.hasProblemAnalysis) {
      schema.currentChallenges = [
        {
          category: "カテゴリ",
          description: "説明",
          impact: "high/medium/low"
        }
      ];
    }

    if (classification.contentAreas.hasSolutionProposal) {
      schema.idealState = ["理想状態1", "理想状態2"];
      schema.proposedSolutions = [
        {
          problemCategory: "問題カテゴリ",
          solution: "解決策",
          benefits: ["効果1", "効果2"]
        }
      ];
    }

    if (classification.contentAreas.hasROIData) {
      schema.roi = {
        investment: [{ item: "項目", amount: "数値" }],
        returns: [{ item: "項目", amount: "数値", timeframe: "期間" }],
        percentage: "数値"
      };
    }

    if (classification.contentAreas.hasTimeline) {
      schema.timeline = [
        {
          date: "日付",
          title: "タイトル",
          description: "説明"
        }
      ];
    }

    if (classification.contentAreas.hasNextSteps) {
      schema.nextSteps = [
        {
          title: "タイトル",
          description: "説明",
          deadline: "期限",
          priority: "high/medium/low"
        }
      ];
    }

    if (classification.contentAreas.hasComparison) {
      schema.comparisonData = [
        {
          category: "比較項目",
          current: "現状",
          proposed: "提案"
        }
      ];
    }

    if (classification.contentAreas.hasMetrics) {
      schema.metrics = [
        {
          name: "メトリクス名",
          value: "数値",
          unit: "単位",
          trend: "up/down/stable"
        }
      ];
    }

    if (classification.contentAreas.hasStakeholderInfo) {
      schema.stakeholders = [
        {
          name: "ステークホルダー名",
          role: "役割",
          impact: "high/medium/low"
        }
      ];
    }

    if (classification.contentAreas.hasTechnicalDetails) {
      schema.technicalDetails = [
        {
          component: "技術要素",
          description: "説明",
          complexity: "high/medium/low"
        }
      ];
    }

    if (classification.contentAreas.hasRiskAnalysis) {
      schema.risks = [
        {
          risk: "リスク内容",
          probability: "high/medium/low",
          impact: "high/medium/low",
          mitigation: "対策"
        }
      ];
    }

    return JSON.stringify(schema, null, 2);
  }

  /**
   * 分類結果に基づいて動的なプロンプトを生成
   */
  private generateDynamicPrompt(classification: MeetingClassification, meetingNotes: string): string {
    const contentAreasText = Object.entries(classification.contentAreas)
      .filter(([_, value]) => value)
      .map(([key, _]) => {
        const labels: Record<string, string> = {
          hasCompanyInfo: '会社・組織情報',
          hasProblemAnalysis: '課題・問題分析',
          hasSolutionProposal: 'ソリューション・解決策',
          hasROIData: 'ROI・投資対効果',
          hasTimeline: 'タイムライン・スケジュール',
          hasNextSteps: '次のステップ・アクション',
          hasComparison: '比較・対比',
          hasProcessFlow: 'プロセス・フロー',
          hasMetrics: 'メトリクス・指標',
          hasRiskAnalysis: 'リスク分析',
          hasStakeholderInfo: 'ステークホルダー情報',
          hasTechnicalDetails: '技術的詳細'
        };
        return `- ${labels[key] || key}`;
      })
      .join('\n');

    const extractionPriorityText = classification.extractionPriority
      .map((item, index) => `${index + 1}. ${item}`)
      .join('\n');

    const dynamicSchema = this.generateDynamicSchema(classification);

    return DYNAMIC_ANALYZE_PROMPT_TEMPLATE
      .replace('{meetingType}', classification.meetingType)
      .replace('{primaryThemes}', classification.primaryThemes.join(', '))
      .replace('{confidence}', classification.confidence.toString())
      .replace('{contentAreas}', contentAreasText)
      .replace('{meetingNotes}', meetingNotes)
      .replace('{extractionPriority}', extractionPriorityText)
      .replace('{dynamicSchema}', dynamicSchema);
  }

  /**
   * 分類結果がない場合のフォールバックプロンプト
   */
  private generateFallbackPrompt(meetingNotes: string): string {
    return `
あなたは議事録を分析して、プレゼンテーション資料作成に必要な情報を抽出する専門家です。
以下の議事録から、重要な情報を構造化して抽出してください。

議事録:
${meetingNotes}

以下の基本情報を抽出してください：
1. 会社名（クライアント名）
2. 主要なトピック・プロジェクト名
3. 現状の課題（存在する場合）
4. 理想的な状態・目標（存在する場合）
5. 提案されている解決策（存在する場合）
6. ROI情報（存在する場合）
7. タイムライン（存在する場合）
8. 今後のステップ（存在する場合）

【重要】以下のJSON形式でのみ回答してください：

{
  "companyName": "会社名",
  "mainTopic": "メイントピック",
  "currentChallenges": [
    {
      "category": "カテゴリ",
      "description": "説明",
      "impact": "high"
    }
  ],
  "idealState": ["理想状態1", "理想状態2"],
  "proposedSolutions": [
    {
      "problemCategory": "問題カテゴリ",
      "solution": "解決策",
      "benefits": ["効果1", "効果2"]
    }
  ],
  "roi": {
    "investment": [{"item": "項目", "amount": 数値}],
    "returns": [{"item": "項目", "amount": 数値, "timeframe": "期間"}],
    "percentage": 数値
  },
  "timeline": [
    {
      "date": "日付",
      "title": "タイトル",
      "description": "説明"
    }
  ],
  "nextSteps": [
    {
      "title": "タイトル",
      "description": "説明",
      "deadline": "期限",
      "priority": "high"
    }
  ]
}

注意事項：
- 議事録に含まれていない情報は空の配列[]や適切なデフォルト値を使用してください
- 影響度と優先度は必ず'high'/'medium'/'low'のいずれかを使用してください
- 数値フィールドには必ず数値を設定してください
`;
  }

  async execute(input: AnalyzeNotesInput & { classification?: MeetingClassification }): Promise<AnalyzeNotesOutput> {
    try {
      console.log('AnalyzeNotesAgent: 議事録の分析を開始します');

      // 分類結果がある場合は動的プロンプトを使用、ない場合はフォールバック
      let prompt: string;
      if (input.classification) {
        console.log('AnalyzeNotesAgent: 分類結果に基づく動的分析を実行します');
        console.log('AnalyzeNotesAgent: 会議タイプ:', input.classification.meetingType);
        console.log('AnalyzeNotesAgent: 主要テーマ:', input.classification.primaryThemes);
        prompt = this.generateDynamicPrompt(input.classification, input.meetingNotes);
      } else {
        console.log('AnalyzeNotesAgent: 分類結果がないため、フォールバック分析を実行します');
        prompt = this.generateFallbackPrompt(input.meetingNotes);
      }

      const response = await this.model.invoke([
        {
          role: 'system',
          content: prompt
        },
        {
          role: 'user',
          content: '議事録を分析し、指定された形式で情報を抽出してください。'
        }
      ]);

      console.log('AnalyzeNotesAgent: AIからの応答を受信しました');
      // レスポンスからJSONを抽出
      const content = response.content as string;
      let extractedInfo;
      
      try {
        // 複数パターンでJSON抽出を試みる
        let jsonStr = '';
        
        // パターン1: ```json ... ``` 
        const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonStr = jsonMatch[1];
        } 
        // パターン2: 全体がJSONの場合（直接パース）
        else if (content.trim().startsWith('{')) {
          jsonStr = content.trim();
        }
        // パターン3: { ... } の最初のJSONオブジェクト（より柔軟に）
        else {
          // 開始の { を見つける
          const startIndex = content.indexOf('{');
          if (startIndex !== -1) {
            let braceCount = 0;
            let endIndex = -1;
            
            for (let i = startIndex; i < content.length; i++) {
              if (content[i] === '{') {
                braceCount++;
              } else if (content[i] === '}') {
                braceCount--;
                if (braceCount === 0) {
                  endIndex = i;
                  break;
                }
              }
            }
            
            if (endIndex !== -1) {
              jsonStr = content.substring(startIndex, endIndex + 1);
            }
          }
        }
        
        if (jsonStr) {
          console.log('AnalyzeNotesAgent: 抽出されたJSON文字列の先頭部分:', jsonStr.substring(0, 200) + '...');
          extractedInfo = JSON.parse(jsonStr.trim());
          console.log('AnalyzeNotesAgent: JSONの解析に成功しました');
        } else {
          throw new Error('JSON not found in response');
        }
      } catch (parseError) {
        console.error('AnalyzeNotesAgent: JSONの解析に失敗しました:', parseError);
        console.error('AnalyzeNotesAgent: 元の応答:', content);
        
        // JSONパースが失敗した場合、直接contentをJSONとしてパースを試みる
        try {
          console.log('AnalyzeNotesAgent: 直接パースを試行します');
          extractedInfo = JSON.parse(content.trim());
          console.log('AnalyzeNotesAgent: 直接パースに成功しました');
        } catch (directParseError) {
          console.error('AnalyzeNotesAgent: 直接パースも失敗しました:', directParseError);
          
          // 全てのパース方法が失敗した場合、デフォルト値でフォールバック
          console.log('AnalyzeNotesAgent: 全パース方法が失敗、デフォルト値を使用します');
          extractedInfo = {
            companyName: "不明",
            mainTopic: "不明",
            currentChallenges: [],
            idealState: [],
            proposedSolutions: [],
            roi: { investment: [], returns: [], percentage: 0 },
            timeline: [],
            nextSteps: []
          };
          console.log('AnalyzeNotesAgent: デフォルト値の設定が完了しました');
        }
      }
      
      // Zodでバリデーション
      console.log('AnalyzeNotesAgent: データのバリデーションを開始します');
      console.log('AnalyzeNotesAgent: 受信したデータ:', JSON.stringify(extractedInfo, null, 2));
      
      let validatedInfo;
      
      try {
        validatedInfo = extractedInfoSchema.parse(extractedInfo);
        console.log('AnalyzeNotesAgent: データのバリデーションに成功しました');
      } catch (validationError) {
        console.error('AnalyzeNotesAgent: バリデーションエラーの詳細:', validationError);
        // バリデーションエラーが発生した場合、デフォルト値を設定
        console.log('AnalyzeNotesAgent: デフォルト値を使用します');
        validatedInfo = {
          companyName: extractedInfo.companyName || "不明",
          mainTopic: extractedInfo.mainTopic || "不明",
          currentChallenges: Array.isArray(extractedInfo.currentChallenges) ? extractedInfo.currentChallenges : [],
          idealState: Array.isArray(extractedInfo.idealState) ? extractedInfo.idealState : [],
          proposedSolutions: Array.isArray(extractedInfo.proposedSolutions) ? extractedInfo.proposedSolutions : [],
          roi: extractedInfo.roi || { investment: [], returns: [], percentage: 0 },
          timeline: Array.isArray(extractedInfo.timeline) ? extractedInfo.timeline : [],
          nextSteps: Array.isArray(extractedInfo.nextSteps) ? extractedInfo.nextSteps : []
        };
        console.log('AnalyzeNotesAgent: デフォルト値の適用が完了しました');
      }
      
      return {
        extractedInfo: validatedInfo
      };
    } catch (error) {
      console.error('AnalyzeNotesAgent: エラーが発生しました:', error);
      throw error;
    }
  }
}
