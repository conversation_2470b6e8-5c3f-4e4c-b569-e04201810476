import { ChatOpenAI } from '@langchain/openai';
import { z } from 'zod';
import { extractedInfoSchema } from '../schemas';
import { AnalyzeNotesInput, AnalyzeNotesOutput } from '../types';

const ANALYZE_PROMPT = `
あなたは議事録を分析して、プレゼンテーション資料作成に必要な情報を抽出する専門家です。
以下の議事録から、重要な情報を構造化して抽出してください。

議事録:
{meetingNotes}

以下の情報を抽出してください：
1. 会社名（クライアント名）
2. 主要なトピック・プロジェクト名
3. 現状の課題（カテゴリ、説明、影響度は'high'/'medium'/'low'で指定）
4. 理想的な状態・目標
5. 提案されている解決策（課題カテゴリ、解決策、期待される効果）
6. ROI情報（投資額、期待リターン、ROI率）
7. タイムライン（日付、マイルストーン）
8. 今後のステップ（タスク、説明、期限、優先度は'high'/'medium'/'low'で指定）

【重要】以下のJSON形式でのみ回答してください。説明文やMarkdownは含めず、純粋なJSONオブジェクトのみを返してください：

{
  "companyName": "会社名",
  "mainTopic": "メイントピック",
  "currentChallenges": [
    {
      "category": "カテゴリ",
      "description": "説明",
      "impact": "high"
    }
  ],
  "idealState": ["理想状態1", "理想状態2"],
  "proposedSolutions": [
    {
      "problemCategory": "問題カテゴリ",
      "solution": "解決策",
      "benefits": ["効果1", "効果2"]
    }
  ],
  "roi": {
    "investment": [{"item": "項目", "amount": 数値}],
    "returns": [{"item": "項目", "amount": 数値, "timeframe": "期間"}],
    "percentage": 数値
  },
  "timeline": [
    {
      "date": "日付",
      "title": "タイトル",
      "description": "説明"
    }
  ],
  "nextSteps": [
    {
      "title": "タイトル",
      "description": "説明",
      "deadline": "期限",
      "priority": "high"
    }
  ]
}

注意事項：
- 影響度と優先度は必ず'high'/'medium'/'low'のいずれかを使用してください
- 情報が不足している場合は空の配列[]や適切なデフォルト値を使用してください
- 数値フィールドには必ず数値を設定してください
`;

export class AnalyzeNotesAgent {
  private model: ChatOpenAI;

  constructor() {
    this.model = new ChatOpenAI({
      modelName: 'gpt-4.1-mini',  // より高性能なモデルに変更
      temperature: 0.1,     // 出力の安定性を向上
      timeout: 60000        // タイムアウトを設定
    });
  }

  async execute(input: AnalyzeNotesInput): Promise<AnalyzeNotesOutput> {
    try {
      console.log('AnalyzeNotesAgent: 議事録の分析を開始します');
      const response = await this.model.invoke([
        {
          role: 'system',
          content: ANALYZE_PROMPT
        },
        {
          role: 'user',
          content: input.meetingNotes
        }
      ]);

      console.log('AnalyzeNotesAgent: AIからの応答を受信しました');
      // レスポンスからJSONを抽出
      const content = response.content as string;
      let extractedInfo;
      
      try {
        // 複数パターンでJSON抽出を試みる
        let jsonStr = '';
        
        // パターン1: ```json ... ``` 
        const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonStr = jsonMatch[1];
        } 
        // パターン2: 全体がJSONの場合（直接パース）
        else if (content.trim().startsWith('{')) {
          jsonStr = content.trim();
        }
        // パターン3: { ... } の最初のJSONオブジェクト（より柔軟に）
        else {
          // 開始の { を見つける
          const startIndex = content.indexOf('{');
          if (startIndex !== -1) {
            let braceCount = 0;
            let endIndex = -1;
            
            for (let i = startIndex; i < content.length; i++) {
              if (content[i] === '{') {
                braceCount++;
              } else if (content[i] === '}') {
                braceCount--;
                if (braceCount === 0) {
                  endIndex = i;
                  break;
                }
              }
            }
            
            if (endIndex !== -1) {
              jsonStr = content.substring(startIndex, endIndex + 1);
            }
          }
        }
        
        if (jsonStr) {
          console.log('AnalyzeNotesAgent: 抽出されたJSON文字列の先頭部分:', jsonStr.substring(0, 200) + '...');
          extractedInfo = JSON.parse(jsonStr.trim());
          console.log('AnalyzeNotesAgent: JSONの解析に成功しました');
        } else {
          throw new Error('JSON not found in response');
        }
      } catch (parseError) {
        console.error('AnalyzeNotesAgent: JSONの解析に失敗しました:', parseError);
        console.error('AnalyzeNotesAgent: 元の応答:', content);
        
        // JSONパースが失敗した場合、直接contentをJSONとしてパースを試みる
        try {
          console.log('AnalyzeNotesAgent: 直接パースを試行します');
          extractedInfo = JSON.parse(content.trim());
          console.log('AnalyzeNotesAgent: 直接パースに成功しました');
        } catch (directParseError) {
          console.error('AnalyzeNotesAgent: 直接パースも失敗しました:', directParseError);
          
          // 全てのパース方法が失敗した場合、デフォルト値でフォールバック
          console.log('AnalyzeNotesAgent: 全パース方法が失敗、デフォルト値を使用します');
          extractedInfo = {
            companyName: "不明",
            mainTopic: "不明",
            currentChallenges: [],
            idealState: [],
            proposedSolutions: [],
            roi: { investment: [], returns: [], percentage: 0 },
            timeline: [],
            nextSteps: []
          };
          console.log('AnalyzeNotesAgent: デフォルト値の設定が完了しました');
        }
      }
      
      // Zodでバリデーション
      console.log('AnalyzeNotesAgent: データのバリデーションを開始します');
      console.log('AnalyzeNotesAgent: 受信したデータ:', JSON.stringify(extractedInfo, null, 2));
      
      let validatedInfo;
      
      try {
        validatedInfo = extractedInfoSchema.parse(extractedInfo);
        console.log('AnalyzeNotesAgent: データのバリデーションに成功しました');
      } catch (validationError) {
        console.error('AnalyzeNotesAgent: バリデーションエラーの詳細:', validationError);
        // バリデーションエラーが発生した場合、デフォルト値を設定
        console.log('AnalyzeNotesAgent: デフォルト値を使用します');
        validatedInfo = {
          companyName: extractedInfo.companyName || "不明",
          mainTopic: extractedInfo.mainTopic || "不明",
          currentChallenges: Array.isArray(extractedInfo.currentChallenges) ? extractedInfo.currentChallenges : [],
          idealState: Array.isArray(extractedInfo.idealState) ? extractedInfo.idealState : [],
          proposedSolutions: Array.isArray(extractedInfo.proposedSolutions) ? extractedInfo.proposedSolutions : [],
          roi: extractedInfo.roi || { investment: [], returns: [], percentage: 0 },
          timeline: Array.isArray(extractedInfo.timeline) ? extractedInfo.timeline : [],
          nextSteps: Array.isArray(extractedInfo.nextSteps) ? extractedInfo.nextSteps : []
        };
        console.log('AnalyzeNotesAgent: デフォルト値の適用が完了しました');
      }
      
      return {
        extractedInfo: validatedInfo
      };
    } catch (error) {
      console.error('AnalyzeNotesAgent: エラーが発生しました:', error);
      throw error;
    }
  }
}
