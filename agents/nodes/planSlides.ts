import { ChatOpenAI } from '@langchain/openai';
import { slideStructureSchema } from '../schemas';
import { PlanSlidesInput, PlanSlidesOutput } from '../types';
import { MeetingClassification } from './classifyMeetingNotes';
import {
  getCoreSlides,
  getOptionalSlides,
  getAllSlideTemplates
} from '../templates/slideTemplate';

const PLAN_SLIDES_PROMPT = `
あなたはプレゼンテーション構成の専門家です。
抽出された情報を基に、効果的なスライド構成を計画してください。

【動的コンポーネント選択システム】
このシステムでは、利用可能なスライドコンポーネントが動的に発見・管理されています。
議事録の内容に最も適したコンポーネントを選択してください。

【ハイブリッド方式】
1. 必須スライド（CORE）: 必ず含める
2. オプションスライド（OPTIONAL）: 内容に応じて動的に選択

=== 必須スライド（必ず含める） ===
{coreSlides}

=== オプションスライド（内容に応じて動的選択） ===
{optionalSlides}

【動的選択基準】
- 抽出された情報に基づいて、最も効果的なプレゼンテーションになるオプションスライドを選択
- 各スライドの「条件」を参考に、含めるべきかを判断
- 情報が不足している場合は無理に含めない
- 5-8枚程度の適切な長さに調整
- 新しいコンポーネントが追加されている可能性があるため、利用可能なコンポーネントを最大限活用

抽出された情報:
{extractedInfo}

【出力形式】
選択したスライドを order 順に並べて、以下のJSON配列で出力してください：

[
  {
    "slideType": "cover",
    "component": "CoverSlide",
    "props": {
      "companyName": "会社名",
      "title": "プレゼンテーションタイトル"
    },
    "order": 1
  }
]

重要事項：
- component フィールドには上記リストの正確なコンポーネント名を使用してください
- props は空のオブジェクト {} でも構いません（後でGenerateSlidesAgentが詳細を生成します）
- 動的コンポーネントシステムにより、最適なスライド構成を選択してください
`;

export class PlanSlidesAgent {
  private model: ChatOpenAI;

  constructor() {
    this.model = new ChatOpenAI({
      modelName: 'gpt-4.1-mini',  // より高性能なモデルに変更
      temperature: 0.1,     // 出力の安定性を向上
      timeout: 60000        // タイムアウトを設定
    });
  }

  async execute(input: PlanSlidesInput): Promise<PlanSlidesOutput> {
    try {
      console.log('PlanSlidesAgent: スライド構成の計画を開始します');

      // 動的にコンポーネント情報を取得
      const coreSlides = getCoreSlides();
      const optionalSlides = getOptionalSlides();

      // 分類結果に基づいてスライド選択を最適化
      let filteredOptionalSlides = optionalSlides;
      let additionalContext = '';

      if (input.classification) {
        console.log('PlanSlidesAgent: 分類結果に基づくスライド選択を実行します');
        console.log('PlanSlidesAgent: 会議タイプ:', input.classification.meetingType);
        console.log('PlanSlidesAgent: 推奨スライドタイプ:', input.classification.recommendedSlideTypes);

        // 分類結果に基づいてオプションスライドをフィルタリング
        filteredOptionalSlides = this.filterSlidesByClassification(optionalSlides, input.classification);
        additionalContext = this.generateClassificationContext(input.classification);
      }

      // テンプレート情報を文字列化
      const coreInfo = coreSlides.map(slide =>
        `- ${slide.component}: ${slide.description}`
      ).join('\n');

      const optionalInfo = filteredOptionalSlides.map(slide =>
        `- ${slide.component}: ${slide.description}\n  条件: ${slide.conditions?.join(', ') || 'なし'}`
      ).join('\n\n');

      console.log('PlanSlidesAgent: 利用可能なコンポーネント数:', coreSlides.length + filteredOptionalSlides.length);

      const prompt = PLAN_SLIDES_PROMPT
        .replace('{coreSlides}', coreInfo)
        .replace('{optionalSlides}', optionalInfo)
        .replace('{extractedInfo}', JSON.stringify(input.extractedInfo, null, 2)) +
        (additionalContext ? `\n\n${additionalContext}` : '');

      const response = await this.model.invoke([
        {
          role: 'system',
          content: prompt
        },
        {
          role: 'user',
          content: 'プレゼンテーションに最適なスライド構成を選択し、JSON配列で出力してください。'
        }
      ]);

      console.log('PlanSlidesAgent: AIからの応答を受信しました');
      
      // レスポンスからJSONを抽出
      const content = response.content as string;
      let slideStructure;
      
      try {
        // 複数パターンでJSON抽出を試みる
        let jsonStr = '';
        
        // パターン1: ```json ... ``` 
        const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonStr = jsonMatch[1];
        } 
        // パターン2: 全体がJSONの場合（直接パース）
        else if (content.trim().startsWith('[')) {
          jsonStr = content.trim();
        }
        // パターン3: [ ... ] の最初のJSON配列
        else {
          const startIndex = content.indexOf('[');
          if (startIndex !== -1) {
            let bracketCount = 0;
            let endIndex = -1;
            
            for (let i = startIndex; i < content.length; i++) {
              if (content[i] === '[') {
                bracketCount++;
              } else if (content[i] === ']') {
                bracketCount--;
                if (bracketCount === 0) {
                  endIndex = i;
                  break;
                }
              }
            }
            
            if (endIndex !== -1) {
              jsonStr = content.substring(startIndex, endIndex + 1);
            }
          }
        }
        
        if (jsonStr) {
          slideStructure = JSON.parse(jsonStr.trim());
          console.log('PlanSlidesAgent: JSONの解析に成功しました');
        } else {
          throw new Error('JSON not found in response');
        }
      } catch (parseError) {
        console.error('PlanSlidesAgent: JSONの解析に失敗しました:', parseError);
        console.error('PlanSlidesAgent: 元の応答:', content);
        
        // フォールバック: 基本的なスライド構成
        console.log('PlanSlidesAgent: フォールバック構成を使用します');
        slideStructure = [
          {
            slideType: "cover",
            component: "CoverSlide",
            props: {},
            order: 1
          },
          {
            slideType: "summary",
            component: "ExecutiveSummarySlide", 
            props: {},
            order: 2
          },
          {
            slideType: "problem",
            component: "ProblemAnalysisSlide",
            props: {},
            order: 3
          },
          {
            slideType: "solution",
            component: "SolutionOverviewSlide",
            props: {},
            order: 4
          },
          {
            slideType: "next_steps",
            component: "NextStepsSlide",
            props: {},
            order: 5
          }
        ];
      }
      
      // スライド構成の検証と補正
      slideStructure = this.validateAndCorrectStructure(slideStructure);
      
      // Zodでバリデーション
      console.log('PlanSlidesAgent: データのバリデーションを開始します');
      const validatedStructure = slideStructureSchema.parse(slideStructure);
      console.log('PlanSlidesAgent: データのバリデーションに成功しました');
      
      return {
        slideStructure: validatedStructure.map(slide => ({
          ...slide,
          props: slide.props ?? {}
        }))
      };
    } catch (error) {
      console.error('PlanSlidesAgent: エラーが発生しました:', error);
      throw error;
    }
  }

  /**
   * 分類結果に基づいてオプションスライドをフィルタリング
   */
  private filterSlidesByClassification(optionalSlides: any[], classification: MeetingClassification): any[] {
    // 会議タイプに基づく推奨スライドマッピング
    const meetingTypeSlideMapping: Record<string, string[]> = {
      'sales_presentation': ['ProblemAnalysisSlide', 'SolutionOverviewSlide', 'ROICalculationSlide', 'TimelineSlide'],
      'project_review': ['TimelineSlide', 'MetricsSlide', 'ComparisonTableSlide', 'NextStepsSlide'],
      'problem_solving': ['ProblemAnalysisSlide', 'AsIsSlide', 'SolutionOverviewSlide', 'RiskMatrixSlide'],
      'strategic_planning': ['AsIsSlide', 'TimelineSlide', 'ROICalculationSlide', 'MetricsSlide'],
      'status_update': ['MetricsSlide', 'TimelineSlide', 'ComparisonTableSlide'],
      'client_consultation': ['ProblemAnalysisSlide', 'SolutionOverviewSlide', 'CaseInfoSlide'],
      'product_demo': ['SolutionOverviewSlide', 'ComparisonTableSlide', 'ROICalculationSlide'],
      'budget_planning': ['ROICalculationSlide', 'BudgetBreakdownSlide', 'TimelineSlide']
    };

    // 推奨スライドを取得
    const recommendedComponents = meetingTypeSlideMapping[classification.meetingType] || [];

    // コンテンツエリアに基づく追加フィルタリング
    const contentBasedComponents: string[] = [];
    if (classification.contentAreas.hasProblemAnalysis) contentBasedComponents.push('ProblemAnalysisSlide');
    if (classification.contentAreas.hasSolutionProposal) contentBasedComponents.push('SolutionOverviewSlide', 'AsIsSlide');
    if (classification.contentAreas.hasROIData) contentBasedComponents.push('ROICalculationSlide');
    if (classification.contentAreas.hasTimeline) contentBasedComponents.push('TimelineSlide');
    if (classification.contentAreas.hasComparison) contentBasedComponents.push('ComparisonTableSlide', 'AsIsSlide');
    if (classification.contentAreas.hasMetrics) contentBasedComponents.push('MetricsSlide');
    if (classification.contentAreas.hasRiskAnalysis) contentBasedComponents.push('RiskMatrixSlide');
    if (classification.contentAreas.hasProcessFlow) contentBasedComponents.push('ProcessFlowSlide', 'AutoCycleDiagram');

    // 推奨コンポーネントとコンテンツベースコンポーネントを結合（重複除去）
    const combinedComponents = [...recommendedComponents, ...contentBasedComponents];
    const allRecommendedComponents = combinedComponents.filter((item, index) =>
      combinedComponents.indexOf(item) === index
    );

    // フィルタリング実行
    const filtered = optionalSlides.filter(slide =>
      allRecommendedComponents.includes(slide.component)
    );

    console.log('PlanSlidesAgent: フィルタリング結果:', {
      original: optionalSlides.length,
      filtered: filtered.length,
      recommended: allRecommendedComponents
    });

    return filtered.length > 0 ? filtered : optionalSlides; // フィルタリング結果が空の場合は全て返す
  }

  /**
   * 分類結果に基づく追加コンテキストを生成
   */
  private generateClassificationContext(classification: MeetingClassification): string {
    return `
【会議分析結果】
会議タイプ: ${classification.meetingType}
主要テーマ: ${classification.primaryThemes.join(', ')}
信頼度: ${(classification.confidence * 100).toFixed(1)}%

【推奨スライド戦略】
この会議タイプ（${classification.meetingType}）に最適化されたスライド構成を選択してください。
特に以下の要素を重視してください：
${classification.recommendedSlideTypes.map(type => `- ${type}`).join('\n')}

【利用可能な情報領域】
${Object.entries(classification.contentAreas)
  .filter(([_, value]) => value)
  .map(([key, _]) => {
    const labels: Record<string, string> = {
      hasCompanyInfo: '会社・組織情報',
      hasProblemAnalysis: '課題・問題分析',
      hasSolutionProposal: 'ソリューション・解決策',
      hasROIData: 'ROI・投資対効果',
      hasTimeline: 'タイムライン・スケジュール',
      hasNextSteps: '次のステップ・アクション',
      hasComparison: '比較・対比',
      hasProcessFlow: 'プロセス・フロー',
      hasMetrics: 'メトリクス・指標',
      hasRiskAnalysis: 'リスク分析',
      hasStakeholderInfo: 'ステークホルダー情報',
      hasTechnicalDetails: '技術的詳細'
    };
    return `- ${labels[key] || key}`;
  }).join('\n')}
`;
  }

  /**
   * スライド構成を検証し、必要に応じて補正する（動的コンポーネント対応）
   */
  private validateAndCorrectStructure(structure: any[]): any[] {
    // 動的に必須スライドを取得
    const coreSlides = getCoreSlides();
    const coreComponents = coreSlides.map(slide => slide.component);
    const existingComponents = structure.map(slide => slide.component);

    // 不足している必須スライドを追加
    const missingCoreSlides = coreComponents.filter(component =>
      !existingComponents.includes(component)
    );

    for (const missingComponent of missingCoreSlides) {
      const template = coreSlides.find(slide => slide.component === missingComponent);
      if (template) {
        console.log(`PlanSlidesAgent: 必須スライド ${missingComponent} を追加します`);
        structure.push({
          slideType: template.slideType,
          component: template.component,
          props: {},
          order: template.order
        });
      }
    }
    
    // orderでソート
    structure.sort((a, b) => a.order - b.order);
    
    // 存在しないコンポーネントを除外（動的に取得）
    const allTemplates = getAllSlideTemplates();
    const validComponents = allTemplates.map(t => t.component);
    const validStructure = structure.filter(slide => {
      if (!validComponents.includes(slide.component)) {
        console.warn(`PlanSlidesAgent: 不明なコンポーネント ${slide.component} を除外します`);
        return false;
      }
      return true;
    });
    
    console.log(`PlanSlidesAgent: 最終的なスライド構成: ${validStructure.map(s => s.component).join(', ')}`);
    
    return validStructure;
  }
}
