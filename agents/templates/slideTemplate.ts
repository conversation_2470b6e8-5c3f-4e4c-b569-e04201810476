// スライドテンプレート定義

export interface SlideTemplate {
  component: string;
  slideType: string;
  required: boolean;
  order: number;
  description: string;
  conditions?: string[]; // どんな時に使うべきか
}

// 必須スライド（必ず含まれる）
export const CORE_SLIDES: SlideTemplate[] = [
  {
    component: 'CoverSlide',
    slideType: 'cover',
    required: true,
    order: 1,
    description: '表紙・タイトルスライド'
  },
  {
    component: 'AgendaSlide',
    slideType: 'agenda',
    required: true,
    order: 2,
    description: 'アジェンダ・会議の進行予定'
  },
  {
    component: 'ObjectiveSlide',
    slideType: 'objective',
    required: true,
    order: 3,
    description: '本日の目的・会議の目標'
  },
  {
    component: 'ProblemAnalysisSlide',
    slideType: 'problem',
    required: true,
    order: 4,
    description: '現状・課題の分析'
  },
  {
    component: 'GoalSlide',
    slideType: 'goal',
    required: true,
    order: 5,
    description: '目指すゴール・理想的な状態'
  },
  {
    component: 'DevelopmentFlowSlide',
    slideType: 'development_flow',
    required: true,
    order: 6,
    description: '開発の流れ・プロジェクト進行計画'
  },
  {
    component: 'NextStepsSlide',
    slideType: 'next_steps',
    required: true,
    order: 7,
    description: '次回に向けて・アクションプラン'
  }
];

// 静的にコンポーネント情報を取得する関数
export function getCoreSlides(): SlideTemplate[] {
  return CORE_SLIDES;
}

// オプションスライド（AIが内容に応じて選択）
export const OPTIONAL_SLIDES: SlideTemplate[] = [
  {
    component: 'ExecutiveSummarySlide',
    slideType: 'summary',
    required: false,
    order: 8,
    description: 'エグゼクティブサマリー・要約',
    conditions: ['重要なポイントの要約が必要', '経営陣向け提案']
  },
  {
    component: 'AsIsSlide',
    slideType: 'as_is',
    required: false,
    order: 15,
    description: '現状とあるべき姿の比較',
    conditions: ['現状と理想状態が明確', '変革・改善提案']
  },
  {
    component: 'SolutionOverviewSlide',
    slideType: 'solution',
    required: false,
    order: 20,
    description: 'ソリューション概要・解決策提示',
    conditions: ['具体的な解決策が提案されている', 'サービス・製品紹介']
  },
  {
    component: 'ROICalculationSlide',
    slideType: 'roi',
    required: false,
    order: 25,
    description: 'ROI計算・投資対効果',
    conditions: ['投資額と効果が数値化されている', '経営陣向け提案']
  },
  {
    component: 'TimelineSlide',
    slideType: 'timeline',
    required: false,
    order: 30,
    description: 'タイムライン・実施スケジュール',
    conditions: ['実施スケジュールが明確', 'プロジェクト提案']
  },
  {
    component: 'ComparisonTableSlide',
    slideType: 'comparison',
    required: false,
    order: 35,
    description: '比較表・選択肢比較',
    conditions: ['複数の選択肢を比較', '競合比較']
  },
  {
    component: 'MetricsSlide',
    slideType: 'metrics',
    required: false,
    order: 40,
    description: '指標・KPI・成果測定',
    conditions: ['KPIや指標が定義されている', '成果測定が重要']
  },
  {
    component: 'CaseInfoSlide',
    slideType: 'case_study',
    required: false,
    order: 45,
    description: '事例紹介・ケーススタディ',
    conditions: ['関連事例がある', '実績アピールが重要']
  },
  {
    component: 'VennDiagram',
    slideType: 'venn',
    required: false,
    order: 50,
    description: 'ベン図・関係性の可視化',
    conditions: ['複数要素の関係性を示す', '重複・交差を表現']
  },
  {
    component: 'AutoCycleDiagram',
    slideType: 'cycle',
    required: false,
    order: 55,
    description: 'サイクル図・プロセス図',
    conditions: ['循環的なプロセス', '継続的な改善サイクル']
  }
];

export function getOptionalSlides(): SlideTemplate[] {
  return OPTIONAL_SLIDES;
}

// 全スライドテンプレート
export function getAllSlideTemplates(): SlideTemplate[] {
  return [...CORE_SLIDES, ...OPTIONAL_SLIDES];
}

export const ALL_SLIDE_TEMPLATES = getAllSlideTemplates();

// スライドテンプレート選択のヘルパー関数
export function getSlideTemplateByComponent(component: string): SlideTemplate | undefined {
  return ALL_SLIDE_TEMPLATES.find(template => template.component === component);
}

export function getCoreSlideComponents(): string[] {
  return CORE_SLIDES.map(slide => slide.component);
}

export function getOptionalSlideComponents(): string[] {
  return OPTIONAL_SLIDES.map(slide => slide.component);
}
