import dynamic from 'next/dynamic';
import React from 'react';

// スライドコンポーネントのメタデータ型定義
export interface SlideComponentMetadata {
  component: string;
  slideType: string;
  description: string;
  conditions?: string[];
  required?: boolean;
  order?: number;
  category?: string;
}

// 利用可能なスライドコンポーネントのリスト
// 新しいコンポーネントを追加する際は、ここに追加してください
const AVAILABLE_SLIDE_COMPONENTS: SlideComponentMetadata[] = [
  // 必須コンポーネント
  {
    component: 'CoverSlide',
    slideType: 'cover',
    description: '表紙・タイトルスライド',
    required: true,
    order: 1,
    category: 'core'
  },
  {
    component: 'ExecutiveSummarySlide',
    slideType: 'summary',
    description: 'エグゼクティブサマリー・要約',
    required: true,
    order: 2,
    category: 'core'
  },
  {
    component: 'NextStepsSlide',
    slideType: 'next_steps',
    description: '次のステップ・アクションプラン',
    required: true,
    order: 999,
    category: 'core'
  },
  
  // オプションコンポーネント
  {
    component: 'ProblemAnalysisSlide',
    slideType: 'problem',
    description: '課題分析・問題提起',
    conditions: ['課題が明確に特定されている', '問題解決型の提案'],
    order: 10,
    category: 'analysis'
  },
  {
    component: 'AsIsSlide',
    slideType: 'as_is',
    description: '現状とあるべき姿の比較',
    conditions: ['現状と理想状態が明確', '変革・改善提案'],
    order: 15,
    category: 'analysis'
  },
  {
    component: 'SolutionOverviewSlide',
    slideType: 'solution',
    description: 'ソリューション概要・解決策提示',
    conditions: ['具体的な解決策が提案されている', 'サービス・製品紹介'],
    order: 20,
    category: 'solution'
  },
  {
    component: 'ROICalculationSlide',
    slideType: 'roi',
    description: 'ROI計算・投資対効果',
    conditions: ['投資額と効果が数値化されている', '経営陣向け提案'],
    order: 25,
    category: 'financial'
  },
  {
    component: 'TimelineSlide',
    slideType: 'timeline',
    description: 'タイムライン・実施スケジュール',
    conditions: ['実施スケジュールが明確', 'プロジェクト提案'],
    order: 30,
    category: 'planning'
  },
  {
    component: 'ComparisonTableSlide',
    slideType: 'comparison',
    description: '比較表・選択肢比較',
    conditions: ['複数の選択肢を比較', '競合比較'],
    order: 35,
    category: 'analysis'
  },
  {
    component: 'MetricsSlide',
    slideType: 'metrics',
    description: '指標・KPI・成果測定',
    conditions: ['KPIや指標が定義されている', '成果測定が重要'],
    order: 40,
    category: 'measurement'
  },
  {
    component: 'CaseInfoSlide',
    slideType: 'case_study',
    description: '事例紹介・ケーススタディ',
    conditions: ['関連事例がある', '実績アピールが重要'],
    order: 45,
    category: 'evidence'
  },
  {
    component: 'VennDiagram',
    slideType: 'venn',
    description: 'ベン図・関係性の可視化',
    conditions: ['複数要素の関係性を示す', '重複・交差を表現'],
    order: 50,
    category: 'visualization'
  },
  {
    component: 'AutoCycleDiagram',
    slideType: 'cycle',
    description: 'サイクル図・プロセス図',
    conditions: ['循環的なプロセス', '継続的な改善サイクル'],
    order: 55,
    category: 'visualization'
  }
];

// 動的にコンポーネントをインポートする関数（クライアントサイドでのみ実行）
export function createDynamicSlideComponents(): Record<string, React.ComponentType<any>> {
  // サーバーサイドでは空のオブジェクトを返す
  if (typeof window === 'undefined') {
    return {};
  }

  const components: Record<string, React.ComponentType<any>> = {};

  AVAILABLE_SLIDE_COMPONENTS.forEach(({ component }) => {
    try {
      components[component] = dynamic(() => import(`@/components/slides/${component}`), {
        loading: () => React.createElement('div', {}, `Loading ${component}...`),
        ssr: false
      });
    } catch (error) {
      console.warn(`Failed to load component: ${component}`, error);
    }
  });

  return components;
}

// 利用可能なスライドコンポーネントのメタデータを取得
export function getAvailableSlideComponents(): SlideComponentMetadata[] {
  return AVAILABLE_SLIDE_COMPONENTS;
}

// 特定のカテゴリのコンポーネントを取得
export function getComponentsByCategory(category: string): SlideComponentMetadata[] {
  return AVAILABLE_SLIDE_COMPONENTS.filter(comp => comp.category === category);
}

// 必須コンポーネントを取得
export function getRequiredComponents(): SlideComponentMetadata[] {
  return AVAILABLE_SLIDE_COMPONENTS.filter(comp => comp.required === true);
}

// オプションコンポーネントを取得
export function getOptionalComponents(): SlideComponentMetadata[] {
  return AVAILABLE_SLIDE_COMPONENTS.filter(comp => comp.required !== true);
}

// コンポーネント名から詳細情報を取得
export function getComponentMetadata(componentName: string): SlideComponentMetadata | undefined {
  return AVAILABLE_SLIDE_COMPONENTS.find(comp => comp.component === componentName);
}

// AIエージェント用のコンポーネント情報を文字列として生成
export function generateComponentInfoForAI(): string {
  const coreComponents = getRequiredComponents();
  const optionalComponents = getOptionalComponents();
  
  const coreInfo = coreComponents.map(comp => 
    `- ${comp.component}: ${comp.description}`
  ).join('\n');
  
  const optionalInfo = optionalComponents.map(comp => 
    `- ${comp.component}: ${comp.description}\n  条件: ${comp.conditions?.join(', ') || 'なし'}\n  カテゴリ: ${comp.category || 'その他'}`
  ).join('\n\n');
  
  return `【必須コンポーネント】\n${coreInfo}\n\n【オプションコンポーネント】\n${optionalInfo}`;
}

// 新しいコンポーネントを動的に追加する関数（開発時用）
export function addSlideComponent(metadata: SlideComponentMetadata): void {
  const existingIndex = AVAILABLE_SLIDE_COMPONENTS.findIndex(
    comp => comp.component === metadata.component
  );
  
  if (existingIndex >= 0) {
    AVAILABLE_SLIDE_COMPONENTS[existingIndex] = metadata;
    console.log(`Updated existing component: ${metadata.component}`);
  } else {
    AVAILABLE_SLIDE_COMPONENTS.push(metadata);
    console.log(`Added new component: ${metadata.component}`);
  }
}
