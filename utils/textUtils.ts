/**
 * テキスト制限とフォーマットのユーティリティ
 */

export interface TextLimits {
  title: number;
  description: number;
  shortText: number;
  longText: number;
}

// スライドタイプ別のテキスト制限
export const SLIDE_TEXT_LIMITS: Record<string, TextLimits> = {
  agenda: {
    title: 20,
    description: 60,
    shortText: 15,
    longText: 80
  },
  objective: {
    title: 25,
    description: 80,
    shortText: 20,
    longText: 100
  },
  goal: {
    title: 20,
    description: 70,
    shortText: 15,
    longText: 90
  },
  development: {
    title: 18,
    description: 50,
    shortText: 12,
    longText: 70
  },
  default: {
    title: 25,
    description: 80,
    shortText: 20,
    longText: 100
  }
};

/**
 * テキストを指定された長さで切り詰める
 */
export function truncateText(text: string, maxLength: number, suffix: string = '...'): string {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + suffix;
}

/**
 * スライドタイプに応じてテキストを制限する
 */
export function limitTextForSlide(
  text: string, 
  slideType: string, 
  textType: keyof TextLimits
): string {
  const limits = SLIDE_TEXT_LIMITS[slideType] || SLIDE_TEXT_LIMITS.default;
  return truncateText(text, limits[textType]);
}

/**
 * 配列の要素数を制限する
 */
export function limitArrayItems<T>(array: T[], maxItems: number): T[] {
  return array.slice(0, maxItems);
}

/**
 * 配列の要素数制限と残り表示用の情報を返す
 */
export function limitArrayWithCount<T>(
  array: T[], 
  maxItems: number
): { items: T[]; hasMore: boolean; remainingCount: number } {
  const items = array.slice(0, maxItems);
  const hasMore = array.length > maxItems;
  const remainingCount = Math.max(0, array.length - maxItems);
  
  return { items, hasMore, remainingCount };
}

/**
 * スライドコンテンツの高さ制限を計算する
 */
export function calculateContentHeight(
  totalHeight: number,
  headerHeight: number = 128, // pt-32 = 8rem = 128px
  padding: number = 32, // px-6 = 1.5rem = 24px, but we add some buffer
  footerHeight: number = 0
): number {
  return totalHeight - headerHeight - padding - footerHeight;
}

/**
 * レスポンシブなアイテム数を計算する
 */
export function getResponsiveItemCount(
  containerHeight: number,
  itemHeight: number,
  minItems: number = 3,
  maxItems: number = 8
): number {
  const calculatedItems = Math.floor(containerHeight / itemHeight);
  return Math.max(minItems, Math.min(maxItems, calculatedItems));
}

/**
 * CSS クラス名を動的に生成する
 */
export function getResponsiveTextClass(textLength: number): string {
  if (textLength > 100) return 'text-xs leading-tight';
  if (textLength > 50) return 'text-sm leading-relaxed';
  return 'text-base leading-normal';
}

/**
 * 優先度に応じたスタイルクラスを取得
 */
export function getPriorityStyles(priority?: string): {
  containerClass: string;
  textClass: string;
  iconClass: string;
} {
  switch (priority) {
    case 'high':
      return {
        containerClass: 'bg-red-100 border-red-200',
        textClass: 'text-red-800',
        iconClass: 'text-red-600'
      };
    case 'medium':
      return {
        containerClass: 'bg-yellow-100 border-yellow-200',
        textClass: 'text-yellow-800',
        iconClass: 'text-yellow-600'
      };
    case 'low':
      return {
        containerClass: 'bg-green-100 border-green-200',
        textClass: 'text-green-800',
        iconClass: 'text-green-600'
      };
    default:
      return {
        containerClass: 'bg-blue-100 border-blue-200',
        textClass: 'text-blue-800',
        iconClass: 'text-blue-600'
      };
  }
}

/**
 * スライドアイテムの最適な表示数を決定
 */
export function getOptimalItemCount(
  items: any[],
  slideType: string,
  containerHeight?: number
): number {
  const typeBasedLimits: Record<string, number> = {
    agenda: 8,
    objective: 5,
    goal: 4,
    development: 4,
    default: 6
  };

  const baseLimit = typeBasedLimits[slideType] || typeBasedLimits.default;
  
  // コンテナの高さが指定されている場合は、それに基づいて調整
  if (containerHeight) {
    const itemHeight = 64; // 4rem = 64px (min-h-[4rem])
    const calculatedLimit = Math.floor((containerHeight - 100) / itemHeight);
    return Math.min(baseLimit, Math.max(3, calculatedLimit));
  }
  
  return Math.min(baseLimit, items.length);
}
