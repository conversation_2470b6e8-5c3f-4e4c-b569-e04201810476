'use client';

import React, { useState } from 'react';
import Link from 'next/link';

const sampleMeetings = [
  {
    id: 'sales-meeting',
    title: '営業会議サンプル',
    description: 'ECサイト構築プロジェクトの提案会議',
    tags: ['営業', '提案', 'ECサイト', 'ROI'],
    content: `# 営業会議議事録サンプル

## 会議情報
- **日時**: 2024年7月25日 14:00-15:30
- **参加者**: 
  - 株式会社ABC: 山田太郎（営業部長）、佐藤花子（マーケティング部）
  - 弊社: 田中一郎（PM）、鈴木次郎（リードエンジニア）
- **議題**: 新規ECサイト構築プロジェクトの提案

## 現状・課題

### 1. 販売チャネルの不足
- 現在のウェブサイトは情報提供がメインで、オンライン販売機能がない
- 顧客からのオンライン購入希望に対応できていない
- 競合他社はECサイトで売上を伸ばしている

### 2. 顧客エンゲージメントの低さ
- 顧客データの収集と分析がオフラインに偏っている
- パーソナライズされた施策が打ちにくい
- リピート率の向上が課題

## 目指すゴール

### 短期目標（6ヶ月以内）
- 顧客が24時間いつでもどこでも簡単に商品を購入できるECサイトの構築
- 初年度でオンライン売上比率を全体の15%まで引き上げ

### 中期目標（1年以内）
- 顧客一人ひとりに合わせた商品推薦やキャンペーン情報の提供
- ECサイトを通じて得られるデータを活用した製品開発・マーケティング戦略の実現

## 提案ソリューション

### 1. ヘッドレスコマース技術の活用
- 最新のヘッドレスコマース技術による拡張性と柔軟性に優れたECサイト構築
- 会員登録・ログイン、商品検索・一覧・詳細表示、カート機能、決済機能の実装

### 2. 顧客データプラットフォーム（CDP）との連携
- データ収集・分析基盤の構築
- パーソナライズされた商品推薦エンジンの実装

## ROI・投資対効果

### 初期投資
- システム開発費: 1,200万円
- インフラ構築費: 300万円
- **総投資額: 1,500万円**

### 期待リターン
- 初年度売上増: 2,000万円
- 運営コスト削減: 500万円
- **初年度純利益増: 1,000万円**

### ROI計算
- **投資回収期間: 18ヶ月**
- **ROI: 67%**

## 開発の流れ

### フェーズ1: 要件定義・デザイン（2024年8月1日〜8月15日）
- 詳細な機能要件の確定
- ワイヤーフレーム・デザインカンプの作成と承認

### フェーズ2: 開発・実装（2024年8月15日〜9月30日）
- フロントエンド・バックエンド開発
- 各種API連携
- 決済システム統合

### フェーズ3: テスト・品質保証（2024年10月1日〜10月15日）
- 単体テスト、結合テスト
- ユーザー受け入れテスト（UAT）

### フェーズ4: リリース・運用開始（2024年10月25日〜11月5日）
- 本番環境へのデプロイ
- データ移行
- 初期運用サポート

## 次回に向けて

### アクションアイテム
1. **弊社**: 詳細な提案書および見積書の作成（期限: 8月2日）
2. **ABC様**: 商品情報、画像素材の準備（期限: 8月16日）
3. **双方**: 次回会議（提案書レビュー）の日程調整（期限: 7月29日）

### 次回会議予定
- **日時**: 2024年8月5日 15:00-16:30
- **議題**: 詳細提案書の説明と質疑応答`
  }
];

export default function SamplePage() {
  const [selectedSample, setSelectedSample] = useState(sampleMeetings[0]);
  const [copiedId, setCopiedId] = useState<string | null>(null);

  const copyToClipboard = async (content: string, id: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopiedId(id);
      setTimeout(() => setCopiedId(null), 2000);
    } catch (err) {
      console.error('コピーに失敗しました:', err);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* ヘッダー */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            📋 サンプル議事録
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            以下のサンプル議事録を使って、Spectacle Claudeの機能を体験してください
          </p>
          <Link
            href="/generator"
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <span className="mr-2">🚀</span>
            AI生成を試す
          </Link>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* サンプル一覧 */}
          <div className="lg:col-span-1">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">サンプル一覧</h2>
            <div className="space-y-4">
              {sampleMeetings.map((meeting) => (
                <div
                  key={meeting.id}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                    selectedSample.id === meeting.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 bg-white hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedSample(meeting)}
                >
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {meeting.title}
                  </h3>
                  <p className="text-sm text-gray-600 mb-3">
                    {meeting.description}
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {meeting.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 選択されたサンプルの詳細 */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-lg">
              <div className="p-6 border-b border-gray-200">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">
                      {selectedSample.title}
                    </h2>
                    <p className="text-gray-600">
                      {selectedSample.description}
                    </p>
                  </div>
                  <button
                    onClick={() => copyToClipboard(selectedSample.content, selectedSample.id)}
                    className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                      copiedId === selectedSample.id
                        ? 'bg-green-100 text-green-800'
                        : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                    }`}
                  >
                    {copiedId === selectedSample.id ? '✅ コピー済み' : '📋 コピー'}
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {selectedSample.tags.map((tag) => (
                    <span
                      key={tag}
                      className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>

              <div className="p-6">
                <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                  <pre className="text-sm text-gray-800 whitespace-pre-wrap font-mono">
                    {selectedSample.content}
                  </pre>
                </div>
              </div>

              <div className="p-6 border-t border-gray-200 bg-gray-50">
                <div className="flex flex-col sm:flex-row gap-4">
                  <Link
                    href="/generator"
                    className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center"
                  >
                    このサンプルでスライド生成
                  </Link>
                  <button
                    onClick={() => copyToClipboard(selectedSample.content, selectedSample.id)}
                    className="flex-1 bg-gray-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-700 transition-colors"
                  >
                    {copiedId === selectedSample.id ? 'コピー済み ✅' : 'クリップボードにコピー'}
                  </button>
                </div>
                <p className="text-sm text-gray-500 mt-3 text-center">
                  💡 コピーした後、「AI生成」タブでペーストして試してください
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 使い方ガイド */}
        <div className="mt-16 bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            🎯 使い方ガイド
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">1️⃣</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">サンプルを選択</h3>
              <p className="text-gray-600">
                左側からお好みのサンプル議事録を選択してください
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">2️⃣</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">コピー</h3>
              <p className="text-gray-600">
                「コピー」ボタンでクリップボードにコピーします
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">3️⃣</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">生成</h3>
              <p className="text-gray-600">
                「AI生成」ページでペーストしてスライドを生成
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
