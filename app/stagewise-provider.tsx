'use client';

// Stagewise接続エラーを回避するため、一時的に無効化
// import { StagewiseToolbar } from '@stagewise/toolbar-next';

const stagewiseConfig = {
  plugins: []
};

export default function StagewiseProvider({ children }: { children: React.ReactNode }) {
  return (
    <>
      {children}
      {/* Stagewise接続エラーを回避するため、一時的にコメントアウト */}
      {/* {process.env.NODE_ENV === 'development' && (
        <StagewiseToolbar config={stagewiseConfig} />
      )} */}
    </>
  );
}