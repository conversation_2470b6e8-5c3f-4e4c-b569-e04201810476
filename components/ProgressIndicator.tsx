'use client';

import React, { useEffect, useState } from 'react';

interface ProgressData {
  status: 'idle' | 'classifying' | 'analyzing' | 'planning' | 'reviewing' | 'generating' | 'completed' | 'error';
  currentStep: string;
  totalSteps: number;
  completedSteps: number;
  message: string;
  timestamp: number;
}

interface ProgressIndicatorProps {
  sessionId: string;
  isActive: boolean;
  onComplete?: () => void;
  onError?: (error: string) => void;
}

const stepLabels = {
  classifying: '議事録を分析中',
  analyzing: '情報を抽出中',
  planning: 'スライド構成を計画中',
  reviewing: '構成をレビュー中',
  generating: 'スライドを生成中'
};

const stepIcons = {
  classifying: '📝',
  analyzing: '🔍',
  planning: '📋',
  reviewing: '👀',
  generating: '🎨'
};

export default function ProgressIndicator({ 
  sessionId, 
  isActive, 
  onComplete, 
  onError 
}: ProgressIndicatorProps) {
  const [progress, setProgress] = useState<ProgressData>({
    status: 'idle',
    currentStep: '',
    totalSteps: 5,
    completedSteps: 0,
    message: '待機中',
    timestamp: Date.now()
  });

  useEffect(() => {
    if (!isActive || !sessionId) return;

    const pollProgress = async () => {
      try {
        const response = await fetch(`/api/progress?sessionId=${sessionId}`);
        if (response.ok) {
          const data = await response.json();
          setProgress(data);

          if (data.status === 'completed') {
            onComplete?.();
          } else if (data.status === 'error') {
            onError?.(data.message || 'エラーが発生しました');
          }
        }
      } catch (error) {
        console.error('Progress polling error:', error);
      }
    };

    // 初回実行
    pollProgress();

    // 1秒間隔でポーリング
    const interval = setInterval(pollProgress, 1000);

    return () => clearInterval(interval);
  }, [sessionId, isActive, onComplete, onError]);

  if (!isActive) return null;

  const progressPercentage = progress.totalSteps > 0 
    ? Math.round((progress.completedSteps / progress.totalSteps) * 100)
    : 0;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4 shadow-xl">
        <div className="text-center mb-6">
          <div className="text-4xl mb-4 relative">
            {progress.status === 'completed' ? (
              <div className="animate-bounce">✅</div>
            ) : progress.status === 'error' ? (
              <div className="animate-pulse">❌</div>
            ) : (
              <div className="relative">
                <div className="animate-spin text-blue-500">
                  ⚙️
                </div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-2xl">
                    {stepIcons[progress.status as keyof typeof stepIcons] || '⏳'}
                  </div>
                </div>
              </div>
            )}
          </div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">
            {progress.status === 'completed' ? 'スライド生成完了！' :
             progress.status === 'error' ? 'エラーが発生しました' :
             stepLabels[progress.status as keyof typeof stepLabels] || 'スライドを生成中...'}
          </h2>
          <p className="text-gray-600 text-sm">
            {progress.message}
          </p>
        </div>

        {/* プログレスバー */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>進捗</span>
            <span>{progressPercentage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-blue-600 h-3 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>{progress.completedSteps} / {progress.totalSteps} ステップ</span>
            <span>残り約{Math.max(0, progress.totalSteps - progress.completedSteps)}ステップ</span>
          </div>
        </div>

        {/* ステップ表示 */}
        <div className="space-y-3">
          {Object.entries(stepLabels).map(([step, label], index) => {
            const isCompleted = progress.completedSteps > index;
            const isCurrent = progress.status === step;
            const isPending = progress.completedSteps <= index && !isCurrent;

            return (
              <div 
                key={step}
                className={`flex items-center p-3 rounded-lg transition-colors ${
                  isCompleted ? 'bg-green-50 border border-green-200' :
                  isCurrent ? 'bg-blue-50 border border-blue-200' :
                  'bg-gray-50 border border-gray-200'
                }`}
              >
                <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-3 text-sm relative ${
                  isCompleted ? 'bg-green-500 text-white' :
                  isCurrent ? 'bg-blue-500 text-white' :
                  'bg-gray-300 text-gray-600'
                }`}>
                  {isCompleted ? (
                    <div className="animate-pulse">✓</div>
                  ) : isCurrent ? (
                    <div className="relative">
                      <div className="animate-spin absolute inset-0 border-2 border-white border-t-transparent rounded-full"></div>
                      <div className="text-xs">⚡</div>
                    </div>
                  ) : (
                    index + 1
                  )}
                </div>
                <span className={`text-sm flex-1 ${
                  isCompleted ? 'text-green-800 font-medium' :
                  isCurrent ? 'text-blue-800 font-medium' :
                  'text-gray-600'
                }`}>
                  {label}
                </span>
                {isCurrent && (
                  <div className="ml-auto flex items-center space-x-2">
                    <div className="flex space-x-1">
                      <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                      <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                      <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                    </div>
                    <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* エラー時の詳細 */}
        {progress.status === 'error' && (
          <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800 text-sm">
              {progress.message || 'スライド生成中にエラーが発生しました。もう一度お試しください。'}
            </p>
          </div>
        )}

        {/* 完了時のメッセージ */}
        {progress.status === 'completed' && (
          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-green-800 text-sm text-center">
              スライドの生成が完了しました！編集画面に移動します。
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
