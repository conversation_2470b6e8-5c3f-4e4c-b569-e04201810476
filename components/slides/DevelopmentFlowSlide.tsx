'use client';

import React from 'react';
import SlideLabel from '@/components/slide-parts/SlideLabel';
import SlideKeyMessage from '@/components/slide-parts/SlideKeyMessage';

interface DevelopmentFlowSlideProps {
  label?: string;
  keyMessage?: string;
  title?: string;
  mainTitle?: string;
  phases?: Array<{
    name: string;
    description: string;
    duration: string;
    deliverables: string[];
    status?: 'completed' | 'in-progress' | 'planned';
  }>;
  timeline?: Array<{
    date: string;
    title: string;
    description: string;
    milestone?: boolean;
  }>;
  methodology?: string;
}

export default function DevelopmentFlowSlide({
  label = "開発の流れ",
  keyMessage,
  phases = [],
  timeline = [],
  methodology = "アジャイル開発"
}: DevelopmentFlowSlideProps) {
  // デフォルトフェーズ
  const defaultPhases = [
    {
      name: "要件定義・設計",
      description: "詳細な機能要件の確定とシステム設計",
      duration: "2週間",
      deliverables: ["要件定義書", "システム設計書", "UI/UXデザイン"],
      status: 'planned' as const
    },
    {
      name: "開発・実装",
      description: "フロントエンド・バックエンド開発",
      duration: "6週間",
      deliverables: ["機能実装", "API開発", "データベース構築"],
      status: 'planned' as const
    },
    {
      name: "テスト・品質保証",
      description: "単体テスト、結合テスト、ユーザー受け入れテスト",
      duration: "2週間",
      deliverables: ["テスト仕様書", "テスト結果報告書", "品質保証書"],
      status: 'planned' as const
    },
    {
      name: "リリース・運用開始",
      description: "本番環境へのデプロイと運用開始",
      duration: "1週間",
      deliverables: ["リリース版", "運用マニュアル", "保守体制"],
      status: 'planned' as const
    }
  ];

  const finalPhases = phases.length > 0 ? phases : defaultPhases;
  const displayKeyMessage = keyMessage || `${methodology}手法により、効率的で品質の高い開発を実現します`;

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 border-green-300 text-green-800';
      case 'in-progress': return 'bg-blue-100 border-blue-300 text-blue-800';
      case 'planned': return 'bg-gray-100 border-gray-300 text-gray-800';
      default: return 'bg-gray-100 border-gray-300 text-gray-800';
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'completed': return '✅';
      case 'in-progress': return '🔄';
      case 'planned': return '📋';
      default: return '📋';
    }
  };

  const getStatusText = (status?: string) => {
    switch (status) {
      case 'completed': return '完了';
      case 'in-progress': return '進行中';
      case 'planned': return '予定';
      default: return '予定';
    }
  };

  return (
    <div className="relative w-full h-full bg-white font-sans">
      <SlideLabel label={label} />
      <SlideKeyMessage keyMessage={displayKeyMessage} />

      <div className="pt-32 px-6 w-full h-full overflow-hidden">
        <div className="max-w-5xl mx-auto h-full flex flex-col">
          <div className="flex-1 overflow-y-auto">
            {/* フェーズフロー */}
            <div className="relative">
              {/* 接続線 */}
              <div className="absolute top-8 left-0 right-0 h-1 bg-blue-200 z-0"></div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 relative z-10">
                {finalPhases.map((phase, index) => (
                  <div key={index} className="relative">
                    {/* フェーズカード */}
                    <div className={`p-3 rounded-lg border shadow-sm ${getStatusColor(phase.status)} bg-white`}>
                      {/* ステータスバッジ */}
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-md">{getStatusIcon(phase.status)}</span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(phase.status)}`}>
                          {getStatusText(phase.status)}
                        </span>
                      </div>

                      {/* フェーズ名 */}
                      <h3 className="text-xs font-bold text-gray-800 mb-1 line-clamp-2">
                        {phase.name}
                      </h3>

                      {/* 説明 */}
                      <p className="text-gray-600 text-xs mb-2 leading-relaxed line-clamp-2">
                        {phase.description}
                      </p>

                      {/* 期間 */}
                      <div className="flex items-center text-blue-700 mb-2">
                        <span className="mr-1">⏱️</span>
                        <span className="font-medium text-xs">{phase.duration}</span>
                      </div>

                      {/* 成果物 */}
                      <div>
                        <h4 className="text-xs font-bold text-gray-600 mb-1">主な成果物:</h4>
                        <ul className="space-y-1">
                          {phase.deliverables.slice(0, 2).map((deliverable, idx) => (
                            <li key={idx} className="text-xs text-gray-600 flex items-start">
                              <span className="text-blue-600 mr-1">•</span>
                              <span className="line-clamp-1">{deliverable}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    {/* フェーズ番号 */}
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-5 h-5 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold z-20 text-xs">
                      {index + 1}
                    </div>

                    {/* 矢印（最後以外） */}
                    {index < finalPhases.length - 1 && (
                      <div className="hidden lg:block absolute top-8 -right-1 w-3 h-3 text-blue-400 z-20">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* タイムライン（オプション） */}
            {timeline.length > 0 && (
              <div className="mt-6 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <h3 className="text-md font-bold text-blue-800 mb-3 flex items-center">
                  <span className="mr-2">📅</span>
                  詳細スケジュール
                </h3>
                <div className="space-y-2">
                  {timeline.map((item, index) => (
                    <div key={index} className="flex items-center p-2 bg-white rounded border">
                      <div className="w-14 text-xs font-medium text-blue-700 mr-2">
                        {item.date}
                      </div>
                      <div className="flex-grow min-w-0">
                        <div className="font-medium text-gray-800 text-xs truncate">{item.title}</div>
                        <div className="text-xs text-gray-600 line-clamp-1">{item.description}</div>
                      </div>
                      {item.milestone && (
                        <div className="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs font-medium">
                          マイルストーン
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
