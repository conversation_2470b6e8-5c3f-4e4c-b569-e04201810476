'use client';

import React from 'react';
import SlideLabel from '@/components/slide-parts/SlideLabel';
import SlideKeyMessage from '@/components/slide-parts/SlideKeyMessage';

interface DevelopmentFlowSlideProps {
  label?: string;
  keyMessage?: string;
  title?: string;
  mainTitle?: string;
  phases?: Array<{
    name: string;
    description: string;
    duration: string;
    deliverables: string[];
    status?: 'completed' | 'in-progress' | 'planned';
  }>;
  timeline?: Array<{
    date: string;
    title: string;
    description: string;
    milestone?: boolean;
  }>;
  methodology?: string;
}

export default function DevelopmentFlowSlide({
  label = "開発の流れ",
  keyMessage,
  phases = [],
  timeline = [],
  methodology = "アジャイル開発"
}: DevelopmentFlowSlideProps) {
  // デフォルトフェーズ
  const defaultPhases = [
    {
      name: "要件定義・設計",
      description: "詳細な機能要件の確定とシステム設計",
      duration: "2週間",
      deliverables: ["要件定義書", "システム設計書", "UI/UXデザイン"],
      status: 'planned' as const
    },
    {
      name: "開発・実装",
      description: "フロントエンド・バックエンド開発",
      duration: "6週間",
      deliverables: ["機能実装", "API開発", "データベース構築"],
      status: 'planned' as const
    },
    {
      name: "テスト・品質保証",
      description: "単体テスト、結合テスト、ユーザー受け入れテスト",
      duration: "2週間",
      deliverables: ["テスト仕様書", "テスト結果報告書", "品質保証書"],
      status: 'planned' as const
    },
    {
      name: "リリース・運用開始",
      description: "本番環境へのデプロイと運用開始",
      duration: "1週間",
      deliverables: ["リリース版", "運用マニュアル", "保守体制"],
      status: 'planned' as const
    }
  ];

  const finalPhases = phases.length > 0 ? phases : defaultPhases;
  const displayKeyMessage = keyMessage || `${methodology}手法により、効率的で品質の高い開発を実現します`;

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 border-green-300 text-green-800';
      case 'in-progress': return 'bg-blue-100 border-blue-300 text-blue-800';
      case 'planned': return 'bg-gray-100 border-gray-300 text-gray-800';
      default: return 'bg-gray-100 border-gray-300 text-gray-800';
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'completed': return '✅';
      case 'in-progress': return '🔄';
      case 'planned': return '📋';
      default: return '📋';
    }
  };

  const getStatusText = (status?: string) => {
    switch (status) {
      case 'completed': return '完了';
      case 'in-progress': return '進行中';
      case 'planned': return '予定';
      default: return '予定';
    }
  };

  return (
    <div className="relative w-full h-full bg-white font-sans">
      <SlideLabel label={label} />
      <SlideKeyMessage keyMessage={displayKeyMessage} />

      <div className="pt-32 px-6 w-full" style={{ height: 'calc(100% - 2rem)' }}>
        <div className="max-w-5xl mx-auto h-full flex flex-col">
          <div className="flex-1 overflow-y-auto min-h-0">
            {/* フェーズフロー */}
            <div className="relative pb-4">
              {/* 接続線 */}
              <div className="absolute top-6 left-0 right-0 h-0.5 bg-blue-200 z-0"></div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 relative z-10">
                {finalPhases.slice(0, 4).map((phase, index) => (
                  <div key={index} className="relative">
                    {/* フェーズカード */}
                    <div className={`p-2 rounded-lg border shadow-sm ${getStatusColor(phase.status)} bg-white min-h-[8rem]`}>
                      {/* ステータスバッジ */}
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm">{getStatusIcon(phase.status)}</span>
                        <span className={`px-1 py-0.5 rounded text-xs font-medium ${getStatusColor(phase.status)}`}>
                          {getStatusText(phase.status)}
                        </span>
                      </div>

                      {/* フェーズ名 */}
                      <h3 className="text-xs font-bold text-gray-800 mb-1 leading-tight">
                        {phase.name.length > 15 ? `${phase.name.substring(0, 15)}...` : phase.name}
                      </h3>

                      {/* 説明 */}
                      <p className="text-gray-600 text-xs mb-1 leading-tight">
                        {phase.description.length > 40 ? `${phase.description.substring(0, 40)}...` : phase.description}
                      </p>

                      {/* 期間 */}
                      <div className="flex items-center text-blue-700 mb-1">
                        <span className="mr-1 text-xs">⏱️</span>
                        <span className="font-medium text-xs">{phase.duration}</span>
                      </div>

                      {/* 成果物 */}
                      <div>
                        <h4 className="text-xs font-bold text-gray-600 mb-1">成果物:</h4>
                        <ul className="space-y-0.5">
                          {phase.deliverables.slice(0, 2).map((deliverable, idx) => (
                            <li key={idx} className="text-xs text-gray-600 flex items-start">
                              <span className="text-blue-600 mr-1">•</span>
                              <span className="leading-tight">
                                {deliverable.length > 20 ? `${deliverable.substring(0, 20)}...` : deliverable}
                              </span>
                            </li>
                          ))}
                          {phase.deliverables.length > 2 && (
                            <li className="text-xs text-gray-500">他{phase.deliverables.length - 2}項目...</li>
                          )}
                        </ul>
                      </div>
                    </div>

                    {/* フェーズ番号 */}
                    <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold z-20 text-xs">
                      {index + 1}
                    </div>

                    {/* 矢印（最後以外） */}
                    {index < Math.min(finalPhases.length, 4) - 1 && (
                      <div className="hidden lg:block absolute top-6 -right-1 w-2 h-2 text-blue-400 z-20">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {finalPhases.length > 4 && (
                <div className="text-center mt-3">
                  <span className="text-xs text-gray-500">他 {finalPhases.length - 4} フェーズ...</span>
                </div>
              )}
            </div>

            {/* タイムライン（オプション） */}
            {timeline.length > 0 && (
              <div className="mt-4 p-2 bg-blue-50 rounded-lg border border-blue-200">
                <h3 className="text-sm font-bold text-blue-800 mb-2 flex items-center">
                  <span className="mr-2">📅</span>
                  詳細スケジュール
                </h3>
                <div className="space-y-1">
                  {timeline.slice(0, 4).map((item, index) => (
                    <div key={index} className="flex items-center p-1.5 bg-white rounded border">
                      <div className="w-12 text-xs font-medium text-blue-700 mr-2 flex-shrink-0">
                        {item.date}
                      </div>
                      <div className="flex-grow min-w-0 flex-1">
                        <div className="font-medium text-gray-800 text-xs leading-tight">
                          {item.title.length > 15 ? `${item.title.substring(0, 15)}...` : item.title}
                        </div>
                        <div className="text-xs text-gray-600 leading-tight">
                          {item.description.length > 25 ? `${item.description.substring(0, 25)}...` : item.description}
                        </div>
                      </div>
                      {item.milestone && (
                        <div className="ml-1 px-1 py-0.5 bg-yellow-100 text-yellow-800 rounded text-xs font-medium flex-shrink-0">
                          MS
                        </div>
                      )}
                    </div>
                  ))}
                  {timeline.length > 4 && (
                    <div className="text-xs text-blue-600 text-center py-1">他 {timeline.length - 4} 項目...</div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
