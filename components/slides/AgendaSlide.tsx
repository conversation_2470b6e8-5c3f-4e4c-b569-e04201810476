'use client';

import React from 'react';
import SlideLabel from '@/components/slide-parts/SlideLabel';
import SlideKeyMessage from '@/components/slide-parts/SlideKeyMessage';

interface AgendaSlideProps {
  label?: string;
  keyMessage?: string;
  title?: string;
  mainTitle?: string;
  agendaItems?: Array<{
    number: number;
    title: string;
    description?: string;
    duration?: string;
  }>;
  items?: string[];
  totalDuration?: string;
}

export default function AgendaSlide({
  label = "アジェンダ",
  keyMessage = "本日の進行予定",
  agendaItems = [],
  items = [],
  totalDuration
}: AgendaSlideProps) {
  // itemsからagendaItemsを生成（後方互換性のため）
  const processedItems = agendaItems.length > 0 ? agendaItems :
    items.map((item, index) => ({
      number: index + 1,
      title: item,
      description: undefined,
      duration: undefined
    }));

  // デフォルトアジェンダ
  const defaultItems = [
    { number: 1, title: "本日の目的", description: "会議の目標と期待する成果", duration: "5分"},
    { number: 2, title: "現状・課題", description: "現在の状況と解決すべき課題", duration: "15分"},
    { number: 3, title: "目指すゴール", description: "理想的な状態と目標設定", duration: "10分"},
    { number: 4, title: "開発の流れ", description: "プロジェクトの進行計画", duration: "15分"},
    { number: 5, title: "次回に向けて", description: "アクションアイテムと次のステップ", duration: "10分"}
  ];

  const finalItems = processedItems.length > 0 ? processedItems : defaultItems;

  return (
    <div className="absolute inset-0 w-full h-full bg-white font-sans">
      <SlideLabel label={label} />
      <SlideKeyMessage keyMessage={keyMessage} />

      <div className="pt-32 px-6 w-full h-full overflow-hidden">
        <div className="max-w-5xl mx-auto h-full flex flex-col">
          {totalDuration && (
            <div className="text-center mb-4">
              <div className="inline-flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                <span className="font-medium">予定時間: {totalDuration}</span>
              </div>
            </div>
          )}

          <div className="flex-1 overflow-y-auto">
            <div className="grid gap-3">
              {finalItems.map((item, index) => (
                <div
                  key={index}
                  className="flex items-center p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg shadow-sm"
                >
                  {/* 番号 */}
                  <div className="flex-shrink-0 w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center mr-4">
                    <span className="text-lg font-bold">{item.number}</span>
                  </div>

                  {/* コンテンツ */}
                  <div className="flex-grow min-w-0">
                    <h3 className="text-lg font-bold text-gray-800 mb-1 truncate">
                      {item.title}
                    </h3>
                    {item.description && (
                      <p className="text-gray-600 text-sm line-clamp-2">
                        {item.description}
                      </p>
                    )}
                  </div>

                  {/* 時間 */}
                  {item.duration && (
                    <div className="flex-shrink-0 ml-3">
                      <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                        <span className="font-medium">{item.duration}</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
