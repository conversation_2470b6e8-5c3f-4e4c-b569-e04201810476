'use client';

import React, { useEffect } from 'react';
import { ProblemAnalysisSlideProps } from '@/types/slide-props';
import SlideLabel from '@/components/slide-parts/SlideLabel';
import SlideKeyMessage from '@/components/slide-parts/SlideKeyMessage';

export default function ProblemAnalysisSlide({
  label = '問題分析',
  keyMessage = '',
  problems = [],
  futureProjection,
  // AIが生成する可能性のあるフィールド名に対応
  currentChallenges,
  idealState,
  proposedSolutions,
  ...otherProps
}: ProblemAnalysisSlideProps & { 
  currentChallenges?: any[]; 
  idealState?: string[]; 
  proposedSolutions?: any[];
  [key: string]: any;
}) {
  // デバッグ用：propsの内容をログ出力
  useEffect(() => {
    console.log('ProblemAnalysisSlide props:', {
      label,
      keyMessage,
      problems,
      futureProjection,
      currentChallenges,
      idealState,
      proposedSolutions,
      otherProps
    });
  }, [label, keyMessage, problems, futureProjection, currentChallenges, idealState, proposedSolutions, otherProps]);

  // AIが生成したデータ構造を適切に変換
  const actualProblems = problems?.length > 0 ? problems : 
    currentChallenges?.map(challenge => ({
      category: challenge.category || 'カテゴリ未定義',
      description: challenge.description || '',
      impact: challenge.impact || 'medium',
      details: challenge.details || []
    })) || [];

  const actualFutureProjection = futureProjection || 
    (idealState && idealState.length > 0 ? {
      title: '理想的な状態',
      description: idealState.join('、')
    } : null);

  const getImpactColor = (impact: 'high' | 'medium' | 'low') => {
    switch (impact) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-blue-500';
    }
  };

  const getImpactText = (impact: 'high' | 'medium' | 'low') => {
    switch (impact) {
      case 'high': return '高';
      case 'medium': return '中';
      case 'low': return '低';
    }
  };

  // データが不十分な場合のフォールバック表示
  if (!actualProblems || actualProblems.length === 0) {
    return (
      <div className="absolute inset-0 w-full h-full bg-white font-sans">
        <SlideLabel label={label} />
        <SlideKeyMessage keyMessage={keyMessage} />
        <div className="pt-40 px-8 w-full">
          <div className="flex items-center justify-center h-64">
            <p className="text-gray-500 text-lg">問題分析データがありません</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="absolute inset-0 w-full h-full bg-white font-sans">
      <SlideLabel label={label} />
      <SlideKeyMessage keyMessage={keyMessage} />

      <div className="pt-40 px-8 w-full">
        <div className="max-w-6xl mx-auto">
          {/* Problems Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {actualProblems.map((problem, idx) => (
              <div
                key={idx}
                className="bg-white border-2 border-gray-200 rounded-lg p-5 hover:border-gray-300 transition-colors"
              >
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-lg font-bold text-gray-800">
                    {problem.category}
                  </h3>
                  <span className={`${getImpactColor(problem.impact)} text-white text-xs px-2 py-1 rounded-full font-medium`}>
                    影響度: {getImpactText(problem.impact)}
                  </span>
                </div>
                
                <p className="text-gray-700 mb-3 text-sm leading-relaxed">
                  {problem.description}
                </p>
                
                {problem.details && problem.details.length > 0 && (
                  <ul className="space-y-1">
                    {problem.details.map((detail: string, didx: number) => (
                      <li key={didx} className="flex items-start">
                        <span className="text-gray-400 mr-2">•</span>
                        <span className="text-xs text-gray-600">{detail}</span>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            ))}
          </div>

          {/* Future Projection */}
          {actualFutureProjection && (
            <div className="bg-gradient-to-r from-red-50 to-orange-50 border-l-4 border-red-500 rounded-lg p-6">
              <div className="flex items-start">
                <div className="flex-shrink-0 mr-4">
                  <div className="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xl">!</span>
                  </div>
                </div>
                <div>
                  <h4 className="text-lg font-bold text-gray-800 mb-2">
                    {actualFutureProjection.title}
                  </h4>
                  <p className="text-gray-700 text-sm leading-relaxed">
                    {actualFutureProjection.description}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
