'use client';

import React from 'react';
import { MetricsSlideProps } from '@/types/slide-props';
import SlideLabel from '@/components/slide-parts/SlideLabel';
import SlideKeyMessage from '@/components/slide-parts/SlideKeyMessage';

export default function MetricsSlide({
  label,
  keyMessage,
  metrics,
  layout = 'grid'
}: MetricsSlideProps) {
  const getTrendIcon = (trend?: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up': return '↑';
      case 'down': return '↓';
      case 'stable': return '→';
      default: return '';
    }
  };

  const getTrendColor = (trend?: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up': return 'text-green-500';
      case 'down': return 'text-red-500';
      case 'stable': return 'text-gray-500';
      default: return '';
    }
  };

  return (
    <div className="absolute inset-0 w-full h-full bg-white font-sans">
      <SlideLabel label={label} />
      <SlideKeyMessage keyMessage={keyMessage} />

      <div className="pt-40 px-8 w-full">
        <div className="max-w-6xl mx-auto">
          {layout === 'grid' ? (
            // Grid Layout
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {metrics?.map((metric, idx) => (
                <div
                  key={idx}
                  className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow"
                >
                  <div className="flex items-start justify-between mb-3">
                    <h4 className="text-sm font-bold text-gray-600 uppercase tracking-wider">
                      {metric.label}
                    </h4>
                    {metric.trend && (
                      <span className={`text-2xl font-bold ${getTrendColor(metric.trend)}`}>
                        {getTrendIcon(metric.trend)}
                      </span>
                    )}
                  </div>
                  
                  <div className="mb-3">
                    <span className="text-3xl font-bold text-gray-800">
                      {metric.value}
                    </span>
                    {metric.unit && (
                      <span className="text-lg text-gray-600 ml-1">
                        {metric.unit}
                      </span>
                    )}
                  </div>
                  
                  {metric.target && (
                    <div className="text-sm text-gray-600 mb-2">
                      目標: <span className="font-medium">{metric.target}{metric.unit}</span>
                    </div>
                  )}
                  
                  {metric.description && (
                    <p className="text-xs text-gray-500 leading-relaxed">
                      {metric.description}
                    </p>
                  )}
                </div>
              ))}
            </div>
          ) : (
            // List Layout
            <div className="space-y-4">
              {metrics?.map((metric, idx) => (
                <div
                  key={idx}
                  className="bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="text-lg font-bold text-gray-800 mb-2">
                        {metric.label}
                      </h4>
                      <div className="flex items-baseline gap-4">
                        <div>
                          <span className="text-3xl font-bold text-gray-800">
                            {metric.value}
                          </span>
                          {metric.unit && (
                            <span className="text-lg text-gray-600 ml-1">
                              {metric.unit}
                            </span>
                          )}
                        </div>
                        
                        {metric.target && (
                          <div className="text-sm text-gray-600">
                            (目標: {metric.target}{metric.unit})
                          </div>
                        )}
                      </div>
                      
                      {metric.description && (
                        <p className="text-sm text-gray-600 mt-2">
                          {metric.description}
                        </p>
                      )}
                    </div>
                    
                    {metric.trend && (
                      <div className={`text-4xl font-bold ${getTrendColor(metric.trend)} ml-6`}>
                        {getTrendIcon(metric.trend)}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
