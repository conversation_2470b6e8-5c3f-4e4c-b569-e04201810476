'use client';

import React, { useEffect } from 'react';
import { ExecutiveSummarySlideProps } from '@/types/slide-props';
import SlideLabel from '@/components/slide-parts/SlideLabel';
import SlideKeyMessage from '@/components/slide-parts/SlideKeyMessage';

export default function ExecutiveSummarySlide({
  label = 'エグゼクティブサマリー',
  keyMessage = '',
  sections = [],
  highlightColor = '#143d8f'
}: ExecutiveSummarySlideProps) {
  // デバッグ用：propsの内容をログ出力
  useEffect(() => {
    console.log('ExecutiveSummarySlide props:', {
      label,
      keyMessage,
      sections,
      highlightColor
    });
  }, [label, keyMessage, sections, highlightColor]);

  // sectionsが存在しない場合の安全な処理
  if (!sections || !Array.isArray(sections)) {
    return (
      <div className="absolute inset-0 w-full h-full bg-white font-sans">
        <SlideLabel label={label} />
        <SlideKeyMessage keyMessage={keyMessage} />
        <div className="pt-40 px-8 w-full">
          <div className="flex items-center justify-center h-64">
            <p className="text-gray-500 text-lg">セクションデータがありません</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="absolute inset-0 w-full h-full bg-white font-sans">
      <SlideLabel label={label} />
      <SlideKeyMessage keyMessage={keyMessage} />

      <div className="pt-40 px-8 w-full">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
          {sections.map((section, idx) => (
            <div
              key={idx}
              className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow"
            >
              {/* Icon */}
              {section.icon && (
                <div 
                  className="w-12 h-12 rounded-full flex items-center justify-center mb-4"
                  style={{ backgroundColor: highlightColor }}
                >
                  <span className="text-white text-2xl">{section.icon}</span>
                </div>
              )}
              
              {/* Section Title */}
              <h3 className="text-lg font-bold text-gray-800 mb-3">
                {section.title}
              </h3>
              
              {/* Points */}
              <ul className="space-y-2">
                {(section.points || []).map((point, pidx) => (
                  <li key={pidx} className="flex items-start">
                    <span 
                      className="w-1.5 h-1.5 rounded-full mt-1.5 mr-2 flex-shrink-0"
                      style={{ backgroundColor: highlightColor }}
                    />
                    <span className="text-sm text-gray-700 leading-relaxed">
                      {point}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
