'use client';

import React from 'react';
import { SlideContent } from '@/types/props';
import SlideLabel from '@/components/slide-parts/SlideLabel';
import SlideKeyMessage from '@/components/slide-parts/SlideKeyMessage';
import SlideBody from '@/components/slide-parts/SlideBody';

export default function CaseInfoSlide({
  label,
  keyMessage,
  mainTitle,
  mainContent
}: SlideContent) {
  return (
    <div className="relative w-full h-full">
      <SlideLabel label={label} />
      <SlideKeyMessage keyMessage={keyMessage} />
      <SlideBody
        mainTitle={mainTitle}
        mainContent={mainContent}
      />
    </div>
  );
}