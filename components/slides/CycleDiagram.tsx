'use client';

import React from 'react';
import { FlexBox, Text } from 'spectacle';

interface CycleDiagramProps {
  items?: string[];
  centerLabel?: string;
  radius?: number;
  boxWidth?: number;
  boxHeight?: number;
  circleColor?: string;
  boxColor?: string;
  textColor?: string;
  markColor?: string;
}

const CycleDiagram: React.FC<CycleDiagramProps> = ({ 
  items = ['XXX', 'XXX', 'XXX'], 
  centerLabel = 'XXX',
  radius = 140,
  boxWidth = 140,
  boxHeight = 80,
  circleColor = 'gray.300',
  boxColor = 'gray.400',
  textColor = 'white',
  markColor = 'gray.500'
}) => {
  const centerX = 200;
  const centerY = 200;
  const boxCount = items.length;
  
  const toRadians = (degrees: number) => degrees * Math.PI / 180;
  
  const getBoxAngles = (count: number) => {
    const angleStep = 360 / count;
    return Array.from({ length: count }, (_, i) => -90 + i * angleStep);
  };
  
  const getArrowPositions = (count: number, radius: number) => {
    const angleStep = 360 / count;
    return Array.from({ length: count }, (_, i) => {
      const angle = -90 + angleStep / 2 + i * angleStep;
      const adjustedRadius = radius + 2;
      const x = centerX + adjustedRadius * Math.cos(toRadians(angle));
      const y = centerY + adjustedRadius * Math.sin(toRadians(angle));
      
      return {
        x: x,
        y: y,
        rotation: angle + 90
      };
    });
  };
  
  const boxAngles = getBoxAngles(boxCount);
  const arrowPositions = getArrowPositions(boxCount, radius);
  
  const boxPositions = boxAngles?.map(angle => ({
    x: centerX + radius * Math.cos(toRadians(angle)),
    y: centerY + radius * Math.sin(toRadians(angle))
  }));

  return (
    <FlexBox position="relative" justifyContent="center" alignItems="center" height="400px">
      <div style={{ position: 'relative', width: '400px', height: '400px' }}>
        {/* 循環を示すベース円 */}
        <div
          style={{
            position: 'absolute',
            width: `${radius * 2}px`,
            height: `${radius * 2}px`,
            left: `${centerX - radius}px`,
            top: `${centerY - radius}px`,
            border: '2px solid #d1d5db',
            borderRadius: '50%',
            zIndex: 1
          }}
        />

        {/* 方向マーク */}
        {arrowPositions?.map((arrow, index) => (
          <div
            key={`arrow-${index}`}
            style={{
              position: 'absolute',
              left: `${arrow.x - 16}px`,
              top: `${arrow.y - 16}px`,
              width: '32px',
              height: '32px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transform: `rotate(${arrow.rotation}deg)`,
              zIndex: 2,
              fontSize: '2.25rem',
              fontWeight: '500',
              color: '#6b7280'
            }}
          >
            &gt;
          </div>
        ))}

        {/* ボックス */}
        {boxPositions?.map((position, index) => (
          <div
            key={`box-${index}`}
            style={{
              position: 'absolute',
              width: `${boxWidth}px`,
              height: `${boxHeight}px`,
              left: `${position.x - boxWidth / 2}px`,
              top: `${position.y - boxHeight / 2}px`,
              backgroundColor: '#9ca3af',
              borderRadius: '1rem',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              zIndex: 10
            }}
          >
            <Text color={textColor} fontSize={20} fontWeight="bold">
              {items[index]}
            </Text>
          </div>
        ))}

        {/* 中央ラベル */}
        <div
          style={{
            position: 'absolute',
            left: `${centerX - 30}px`,
            top: `${centerY - 15}px`,
            width: '60px',
            height: '30px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 10
          }}
        >
          <Text color="black" fontSize={20} fontWeight="bold">
            {centerLabel}
          </Text>
        </div>
      </div>
    </FlexBox>
  );
};

export default CycleDiagram; 