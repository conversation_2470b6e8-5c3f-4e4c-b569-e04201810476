'use client';

import React from 'react';
import SlideLabel from '@/components/slide-parts/SlideLabel';
import SlideKeyMessage from '@/components/slide-parts/SlideKeyMessage';

interface GoalSlideProps {
  label?: string;
  keyMessage?: string;
  title?: string;
  mainTitle?: string;
  primaryGoal?: string;
  goals?: Array<{
    title: string;
    description: string;
    metrics?: string;
    timeline?: string;
    priority?: 'high' | 'medium' | 'low';
  }>;
  idealState?: string[];
  successMetrics?: Array<{
    metric: string;
    target: string;
    timeframe: string;
  }>;
}

export default function GoalSlide({
  label = "目指すゴール",
  keyMessage,
  title = "目指すゴール",
  mainTitle,
  primaryGoal,
  goals = [],
  idealState = [],
  successMetrics = []
}: GoalSlideProps) {
  // デフォルトゴール
  const defaultGoals = [
    {
      title: "課題の完全解決",
      description: "現在直面している主要な課題を根本的に解決し、持続可能な状態を実現する",
      metrics: "課題解決率 100%",
      timeline: "3ヶ月以内",
      priority: 'high' as const
    },
    {
      title: "効率性の向上",
      description: "業務プロセスを最適化し、生産性と品質の両方を向上させる",
      metrics: "効率性 30%向上",
      timeline: "6ヶ月以内",
      priority: 'high' as const
    },
    {
      title: "持続的な成長基盤",
      description: "将来の成長に向けた強固な基盤を構築し、スケーラブルな体制を整える",
      metrics: "成長率 20%向上",
      timeline: "12ヶ月以内",
      priority: 'medium' as const
    }
  ];

  // goalsが空の場合、idealStateから構築
  let finalGoals = goals;
  if (goals.length === 0) {
    if (idealState.length > 0) {
      finalGoals = idealState.map((state, index) => ({
        title: `ゴール ${index + 1}`,
        description: state,
        priority: index === 0 ? 'high' as const : 'medium' as const
      }));
    } else {
      finalGoals = defaultGoals;
    }
  }

  const displayKeyMessage = keyMessage || primaryGoal || "理想的な状態を実現するための明確な目標を設定します";

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getPriorityIcon = (priority?: string) => {
    switch (priority) {
      case 'high': return '🔥';
      case 'medium': return '⚡';
      case 'low': return '📈';
      default: return '🎯';
    }
  };

  return (
    <div className="absolute inset-0 w-full h-full bg-white font-sans">
      <SlideLabel label={label} />
      <SlideKeyMessage keyMessage={displayKeyMessage} />

      <div className="pt-40 px-8 w-full">
        <div className="max-w-6xl mx-auto">
          <div className="grid gap-4">
            {finalGoals.map((goal, index) => (
              <div
                key={index}
                className="p-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg shadow-sm hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between">
                  {/* メインコンテンツ */}
                  <div className="flex-grow">
                    <div className="flex items-center mb-2">
                      <span className="text-xl mr-3">{getPriorityIcon(goal.priority)}</span>
                      <h3 className="text-xl font-bold text-gray-800">
                        {goal.title}
                      </h3>
                      {goal.priority && (
                        <span className={`ml-3 px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(goal.priority)}`}>
                          {goal.priority === 'high' ? '高' : goal.priority === 'medium' ? '中' : '低'}
                        </span>
                      )}
                    </div>

                    <p className="text-gray-600 text-sm leading-relaxed mb-3">
                      {goal.description}
                    </p>

                    {/* メトリクスとタイムライン */}
                    <div className="flex flex-wrap gap-3 text-xs">
                      {goal.metrics && (
                        <div className="flex items-center text-blue-700">
                          <span className="mr-1">📊</span>
                          <span className="font-medium">{goal.metrics}</span>
                        </div>
                      )}
                      {goal.timeline && (
                        <div className="flex items-center text-green-700">
                          <span className="mr-1">⏰</span>
                          <span className="font-medium">{goal.timeline}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 番号 */}
                  <div className="flex-shrink-0 ml-4">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold">{index + 1}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 成功指標 */}
          {successMetrics.length > 0 && (
            <div className="mt-8 p-6 bg-green-50 rounded-lg border border-green-200">
              <h3 className="text-lg font-bold text-green-800 mb-4 flex items-center">
                <span className="mr-2">📈</span>
                成功指標（KPI）
              </h3>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {successMetrics.map((metric, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg border border-green-200">
                    <div className="font-bold text-green-800 mb-2 text-sm">{metric.metric}</div>
                    <div className="text-lg font-bold text-black mb-1">{metric.target}</div>
                    <div className="text-xs text-green-600">{metric.timeframe}</div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
