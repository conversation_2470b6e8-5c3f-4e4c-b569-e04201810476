'use client';

import React from 'react';
import SlideLabel from '@/components/slide-parts/SlideLabel';
import SlideKeyMessage from '@/components/slide-parts/SlideKeyMessage';

interface GoalSlideProps {
  label?: string;
  keyMessage?: string;
  title?: string;
  mainTitle?: string;
  primaryGoal?: string;
  goals?: Array<{
    title: string;
    description: string;
    metrics?: string;
    timeline?: string;
    priority?: 'high' | 'medium' | 'low';
  }>;
  idealState?: string[];
  successMetrics?: Array<{
    metric: string;
    target: string;
    timeframe: string;
  }>;
}

export default function GoalSlide({
  label = "目指すゴール",
  keyMessage,
  primaryGoal,
  goals = [],
  idealState = [],
  successMetrics = []
}: GoalSlideProps) {
  // デフォルトゴール
  const defaultGoals = [
    {
      title: "課題の完全解決",
      description: "現在直面している主要な課題を根本的に解決し、持続可能な状態を実現する",
      metrics: "課題解決率 100%",
      timeline: "3ヶ月以内",
      priority: 'high' as const
    },
    {
      title: "効率性の向上",
      description: "業務プロセスを最適化し、生産性と品質の両方を向上させる",
      metrics: "効率性 30%向上",
      timeline: "6ヶ月以内",
      priority: 'high' as const
    },
    {
      title: "持続的な成長基盤",
      description: "将来の成長に向けた強固な基盤を構築し、スケーラブルな体制を整える",
      metrics: "成長率 20%向上",
      timeline: "12ヶ月以内",
      priority: 'medium' as const
    }
  ];

  // goalsが空の場合、idealStateから構築
  let finalGoals = goals;
  if (goals.length === 0) {
    if (idealState.length > 0) {
      finalGoals = idealState.map((state, index) => ({
        title: `ゴール ${index + 1}`,
        description: state,
        priority: index === 0 ? 'high' as const : 'medium' as const
      }));
    } else {
      finalGoals = defaultGoals;
    }
  }

  const displayKeyMessage = keyMessage || primaryGoal || "理想的な状態を実現するための明確な目標を設定します";

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getPriorityIcon = (priority?: string) => {
    switch (priority) {
      case 'high': return '🔥';
      case 'medium': return '⚡';
      case 'low': return '📈';
      default: return '🎯';
    }
  };

  return (
    <div className="relative w-full h-full bg-white font-sans">
      <SlideLabel label={label} />
      <SlideKeyMessage keyMessage={displayKeyMessage} />

      <div className="pt-32 px-6 w-full" style={{ height: 'calc(100% - 2rem)' }}>
        <div className="max-w-5xl mx-auto h-full flex flex-col">
          <div className="flex-1 overflow-y-auto min-h-0">
            <div className="space-y-3 pb-4">
              {finalGoals.slice(0, 4).map((goal, index) => (
                <div
                  key={index}
                  className="p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg shadow-sm min-h-[5rem]"
                >
                  <div className="flex items-start justify-between">
                    {/* メインコンテンツ */}
                    <div className="flex-grow min-w-0 flex-1">
                      <div className="flex items-center mb-1">
                        <span className="text-md mr-2">{getPriorityIcon(goal.priority)}</span>
                        <h3 className="text-md font-bold text-gray-800 leading-tight">
                          {goal.title.length > 20 ? `${goal.title.substring(0, 20)}...` : goal.title}
                        </h3>
                        {goal.priority && (
                          <span className={`ml-2 px-1 py-0.5 rounded text-xs font-medium ${getPriorityColor(goal.priority)}`}>
                            {goal.priority === 'high' ? '高' : goal.priority === 'medium' ? '中' : '低'}
                          </span>
                        )}
                      </div>

                      <p className="text-gray-600 text-xs leading-tight mb-2">
                        {goal.description.length > 70 ? `${goal.description.substring(0, 70)}...` : goal.description}
                      </p>

                      {/* メトリクスとタイムライン */}
                      <div className="flex flex-wrap gap-2 text-xs">
                        {goal.metrics && (
                          <div className="flex items-center text-blue-700">
                            <span className="mr-1">📊</span>
                            <span className="font-medium text-xs">
                              {goal.metrics.length > 15 ? `${goal.metrics.substring(0, 15)}...` : goal.metrics}
                            </span>
                          </div>
                        )}
                        {goal.timeline && (
                          <div className="flex items-center text-green-700">
                            <span className="mr-1">⏰</span>
                            <span className="font-medium text-xs">
                              {goal.timeline.length > 10 ? `${goal.timeline.substring(0, 10)}...` : goal.timeline}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 番号 */}
                    <div className="flex-shrink-0 ml-2">
                      <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center">
                        <span className="text-xs font-bold">{index + 1}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {finalGoals.length > 4 && (
                <div className="text-center py-2">
                  <span className="text-xs text-gray-500">他 {finalGoals.length - 4} 項目...</span>
                </div>
              )}

              {/* 成功指標 */}
              {successMetrics.length > 0 && (
                <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
                  <h3 className="text-sm font-bold text-green-800 mb-2 flex items-center">
                    <span className="mr-2">📈</span>
                    成功指標（KPI）
                  </h3>
                  <div className="grid grid-cols-2 gap-2">
                    {successMetrics.slice(0, 4).map((metric, index) => (
                      <div key={index} className="bg-white p-2 rounded border border-green-200">
                        <div className="font-bold text-green-800 mb-1 text-xs leading-tight">
                          {metric.metric.length > 15 ? `${metric.metric.substring(0, 15)}...` : metric.metric}
                        </div>
                        <div className="text-sm font-bold text-black mb-1">{metric.target}</div>
                        <div className="text-xs text-green-600">{metric.timeframe}</div>
                      </div>
                    ))}
                  </div>
                  {successMetrics.length > 4 && (
                    <div className="text-xs text-green-600 mt-2 text-center">他 {successMetrics.length - 4} 指標...</div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
