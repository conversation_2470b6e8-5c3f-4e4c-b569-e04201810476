'use client';

import React from 'react';
import SlideLabel from '@/components/slide-parts/SlideLabel';
import SlideKeyMessage from '@/components/slide-parts/SlideKeyMessage';

interface ObjectiveSlideProps {
  label?: string;
  keyMessage?: string;
  title?: string;
  mainTitle?: string;
  objectives?: Array<{
    title: string;
    description: string;
    icon?: string;
  }>;
  primaryObjective?: string;
  secondaryObjectives?: string[];
  expectedOutcomes?: string[];
}

export default function ObjectiveSlide({
  label = "本日の目的",
  keyMessage = "本日の会議で達成したい目標を明確にします",
  title = "本日の目的",
  mainTitle,
  objectives = [],
  primaryObjective,
  secondaryObjectives = [],
  expectedOutcomes = []
}: ObjectiveSlideProps) {
  // デフォルトの目的
  const defaultObjectives = [
    {
      title: "現状の課題を共有",
      description: "現在直面している課題や問題点を明確にし、関係者間で認識を統一する",
      icon: "🎯"
    },
    {
      title: "解決策の方向性を決定",
      description: "課題解決に向けた最適なアプローチと実行計画を検討・決定する",
      icon: "💡"
    },
    {
      title: "次のアクションを明確化",
      description: "具体的な次のステップとタイムラインを設定し、責任者を決定する",
      icon: "🚀"
    }
  ];

  // objectivesが空の場合、他のpropsから構築
  let finalObjectives = objectives;
  if (objectives.length === 0) {
    if (primaryObjective) {
      finalObjectives = [
        {
          title: "主要目的",
          description: primaryObjective,
          icon: "🎯"
        },
        ...secondaryObjectives.map((obj, index) => ({
          title: `目的 ${index + 2}`,
          description: obj,
          icon: "📋"
        }))
      ];
    } else {
      finalObjectives = defaultObjectives;
    }
  }

  return (
    <div className="absolute inset-0 w-full h-full bg-white font-sans">
      <SlideLabel label={label} />
      <SlideKeyMessage keyMessage={keyMessage} />

      <div className="pt-40 px-8 w-full">
        <div className="max-w-6xl mx-auto">
          <div className="grid gap-6">
            {finalObjectives.map((objective, index) => (
              <div
                key={index}
                className="flex items-start p-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg shadow-sm hover:shadow-md transition-shadow"
              >
                {/* アイコン */}
                <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-6">
                  <span className="text-2xl">{objective.icon || "📌"}</span>
                </div>

                {/* コンテンツ */}
                <div className="flex-grow">
                  <h3 className="text-xl font-bold text-gray-800 mb-2">
                    {objective.title}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {objective.description}
                  </p>
                </div>

                {/* 番号 */}
                <div className="flex-shrink-0 ml-4">
                  <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center">
                    <span className="font-bold text-sm">{index + 1}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 期待される成果 */}
          {expectedOutcomes.length > 0 && (
            <div className="mt-8 p-6 bg-blue-50 rounded-lg border border-blue-200">
              <h3 className="text-lg font-bold text-blue-800 mb-4 flex items-center">
                <span className="mr-2">🎯</span>
                期待される成果
              </h3>
              <ul className="space-y-2">
                {expectedOutcomes.map((outcome, index) => (
                  <li key={index} className="flex items-start text-blue-700">
                    <span className="text-blue-600 mr-3 mt-1">✓</span>
                    <span className="text-sm">{outcome}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
