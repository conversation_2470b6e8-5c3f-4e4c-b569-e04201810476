'use client';

import React, { useEffect } from 'react';
import { SolutionOverviewSlideProps } from '@/types/slide-props';
import SlideLabel from '@/components/slide-parts/SlideLabel';
import SlideKeyMessage from '@/components/slide-parts/SlideKeyMessage';

export default function SolutionOverviewSlide({
  label = 'ソリューション概要',
  keyMessage = '',
  solutions = [],
  centralConcept,
  // AIが生成する可能性のあるフィールド名に対応
  proposedSolutions,
  ...otherProps
}: SolutionOverviewSlideProps & { 
  proposedSolutions?: any[];
  [key: string]: any;
}) {
  // デバッグ用：propsの内容をログ出力
  useEffect(() => {
    console.log('SolutionOverviewSlide props:', {
      label,
      keyMessage,
      solutions,
      centralConcept,
      proposedSolutions,
      otherProps
    });
  }, [label, keyMessage, solutions, centralConcept, proposedSolutions, otherProps]);

  // AIが生成したデータ構造を適切に変換
  const actualSolutions = solutions?.length > 0 ? solutions : 
    proposedSolutions?.map(solution => ({
      problemCategory: solution.problemCategory || '',
      solution: solution.solution || '',
      benefits: Array.isArray(solution.benefits) ? solution.benefits : []
    })) || [];

  // データが不十分な場合のフォールバック表示
  if (!actualSolutions || actualSolutions.length === 0) {
    return (
      <div className="absolute inset-0 w-full h-full bg-white font-sans">
        <SlideLabel label={label} />
        <SlideKeyMessage keyMessage={keyMessage} />
        <div className="pt-40 px-8 w-full">
          <div className="flex items-center justify-center h-64">
            <p className="text-gray-500 text-lg">ソリューションデータがありません</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="absolute inset-0 w-full h-full bg-white font-sans">
      <SlideLabel label={label} />
      <SlideKeyMessage keyMessage={keyMessage} />

      <div className="pt-40 px-8 w-full h-full">
        <div className="max-w-7xl mx-auto h-full">
          {centralConcept ? (
            // Layout with central concept
            <div className="relative h-full flex items-center justify-center">
              {/* Central Concept */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
                <div className="bg-gradient-to-br from-blue-600 to-purple-600 text-white rounded-full w-48 h-48 flex items-center justify-center shadow-xl">
                  <p className="text-lg font-bold text-center px-4">
                    {centralConcept}
                  </p>
                </div>
              </div>

              {/* Solutions arranged around center */}
              <div className="relative w-full h-full max-w-4xl">
                {actualSolutions.map((solution, idx) => {
                  const angle = (360 / actualSolutions.length) * idx - 90;
                  const radius = 280;
                  const x = Math.cos(angle * Math.PI / 180) * radius;
                  const y = Math.sin(angle * Math.PI / 180) * radius;
                  
                  return (
                    <div
                      key={idx}
                      className="absolute w-64 transform -translate-x-1/2 -translate-y-1/2"
                      style={{
                        left: `calc(50% + ${x}px)`,
                        top: `calc(50% + ${y}px)`
                      }}
                    >
                      <div className="bg-white border-2 border-gray-200 rounded-lg p-4 shadow-md hover:shadow-lg transition-shadow">
                        <h4 className="text-sm font-bold text-gray-600 mb-1">
                          {solution.problemCategory}
                        </h4>
                        <p className="text-base font-bold text-gray-800 mb-2">
                          {solution.solution}
                        </p>
                        <ul className="space-y-1">
                          {(solution.benefits || []).map((benefit: string, bidx: number) => (
                            <li key={bidx} className="text-xs text-gray-600 flex items-start">
                              <span className="text-blue-500 mr-1">✓</span>
                              <span>{benefit}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                      {/* Connection line to center */}
                      <svg
                        className="absolute inset-0 w-full h-full pointer-events-none"
                        style={{ transform: 'translate(-50%, -50%)', left: '50%', top: '50%' }}
                      >
                        <line
                          x1="50%"
                          y1="50%"
                          x2={`calc(50% - ${x}px)`}
                          y2={`calc(50% - ${y}px)`}
                          stroke="#e5e7eb"
                          strokeWidth="2"
                          strokeDasharray="5,5"
                        />
                      </svg>
                    </div>
                  );
                })}
              </div>
            </div>
          ) : (
            // Grid layout without central concept
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {actualSolutions.map((solution, idx) => (
                <div
                  key={idx}
                  className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-6 border border-blue-200"
                >
                  <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-1">課題</p>
                    <h3 className="text-lg font-bold text-gray-800">
                      {solution.problemCategory}
                    </h3>
                  </div>
                  
                  <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-1">解決策</p>
                    <p className="text-base font-bold text-blue-700">
                      {solution.solution}
                    </p>
                  </div>
                  
                  <div>
                    <p className="text-sm text-gray-600 mb-2">期待効果</p>
                    <ul className="space-y-1">
                      {(solution.benefits || []).map((benefit: string, bidx: number) => (
                        <li key={bidx} className="text-sm text-gray-700 flex items-start">
                          <span className="text-green-500 mr-2 mt-0.5">•</span>
                          <span>{benefit}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
