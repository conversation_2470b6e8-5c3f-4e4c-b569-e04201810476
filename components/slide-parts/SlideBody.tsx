'use client';

import React from 'react';
import { <PERSON><PERSON><PERSON>Tit<PERSON>, HasMainContent } from '@/types/props';

type SlideBodyProps = HasMainTitle & HasMainContent;

export default function SlideBody({ mainTitle, mainContent }: SlideBodyProps) {
  return (
    <section>
      <div className="bg-gray-100 p-6 rounded-lg shadow-md mt-48">
        <h2 className="text-2xl font-semibold mb-4">{mainTitle}</h2>
        <p className="text-gray-700">{mainContent}</p>
      </div>
    </section>
  );
}