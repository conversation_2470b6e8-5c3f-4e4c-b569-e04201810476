'use client';

import React from 'react';
import { HasKeyMessage } from '@/types/props';

export default function SlideKeyMessage({ keyMessage = '' }: HasKeyMessage) {
  if (!keyMessage) {
    return null;
  }

  return (
    <section className="absolute inset-x-0 top-24 px-8 flex flex-col items-center space-y-3 font-sans">
      <h3 className="key-message text-center">{keyMessage}</h3>
    </section>
  );
}